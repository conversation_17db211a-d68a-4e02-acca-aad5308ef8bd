<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="时间" prop="params.queryDate">
        <el-date-picker
          v-model="queryParams.params.queryDate"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          @change="handleQuery"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="省" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="县" prop="county">
        <el-input
          v-model="queryParams.county"
          placeholder="请输入县"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否有效" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择是否有效"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['person:agent:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <div class="dashboard-editor-container">
      <el-row :gutter="32" style="margin-top: 10px">
        <el-col :xs="24" :sm="24" :lg="6">
          <div class="chart-wrapper">
            <div class="card-panel-title">
              截至{{ queryParams.params.queryDate || "昨日" }}地区用户数
            </div>
            <div class="card-panel-text" style="color: red">
              {{ agentStatistics.yesterdayPeople }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="6">
          <div class="chart-wrapper">
            <div class="card-panel-title">
              截至{{ queryParams.params.queryDate || "昨日" }}地区营业额
            </div>
            <div class="card-panel-text" style="color: green">
              {{ agentStatistics.yesterdayTurnover }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="6">
          <div class="chart-wrapper">
            <div class="card-panel-title">当前地区总用户数</div>
            <div class="card-panel-text" style="color: black">
              {{ agentStatistics.todayPeople }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="6">
          <div class="chart-wrapper">
            <div class="card-panel-title">当前地区总营业额</div>
            <div class="card-panel-text" style="color: #40c9c6">
              {{ agentStatistics.todayTurnover }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-table v-loading="loading" :data="agentList" border>
      <el-table-column label="标识" align="center" prop="sysUser.userId" />
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column
        label="手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="省" align="center" prop="province" />
      <el-table-column label="市" align="center" prop="city" />
      <el-table-column label="县" align="center" prop="county" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="等级" align="center" prop="grade" />
      <el-table-column label="分配比例" align="center" prop="proportion" />
      <el-table-column label="是否有效" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            v-if="scope.row.status == 1"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['person:agent:edit']"
            >取消代理</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改代理信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="420px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="人员" prop="userId">
          <el-select
            v-model="form.userId"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            placeholder="请输入手机号"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.sysUser.phonenumber"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="省份" prop="provinceRegionId">
          <el-select
            v-model="form.provinceRegionId"
            filterable
            placeholder="请选择"
            @change="bindProvinceChange"
          >
            <el-option
              v-for="item in provinceList"
              :label="item.regionName"
              :value="item.regionId"
              :key="item.regionId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="城市" prop="cityRegionId">
          <el-select
            v-model="form.cityRegionId"
            placeholder="请选择"
            filterable
            @change="bindCityChange"
          >
            <el-option
              v-for="item in cityList"
              :label="item.regionName"
              :value="item.regionId"
              :key="item.regionId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区/县" prop="countyRegionId">
          <el-select
            v-model="form.countyRegionId"
            placeholder="请选择"
            filterable
          >
            <el-option
              v-for="item in countyList"
              :label="item.regionName"
              :value="item.regionId"
              :key="item.regionId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="比例" prop="proportion">
          <el-input-number
            v-model="form.proportion"
            :min="0"
            :max="1"
            :precision="2"
            :step="0.1"
          >
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAgent,
  addAgent,
  updateAgent,
  district,
  getAgentStatistics,
} from "@/api/person/agent";
import { listInfo } from "@/api/person/info";
import { get } from "sortablejs";
export default {
  name: "Agent",
  dicts: ["sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代理信息表格数据
      agentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        county: null,
        countyCode: null,
        createDate: null,
        grade: null,
        status: null,
        params: {
          queryDate: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [{ required: true, message: "请选择人员", trigger: "change" }],
        provinceRegionId: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
      },
      //地区统计数
      agentStatistics: {
        yesterdayPeople: 0,
        todayPeople: 0,
        yesterdayTurnover: 0,
        todayTurnover: 0,
      },
      provinceList: [],
      cityList: [],
      countyList: [],
      userOptions: [],
    };
  },
  created() {
    this.getList();
    this.getDistrict();
    this.getStatistics();
  },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        listInfo({ phonenumber: query }).then((res) => {
          this.loading = false;
          this.userOptions = res.rows;
        });
      } else {
        this.userOptions = [];
      }
    },

    /** 查询代理信息列表 */
    getList() {
      this.loading = true;
      listAgent(this.queryParams).then((response) => {
        this.agentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getStatistics() {
      getAgentStatistics(this.queryParams).then((response) => {
        this.agentStatistics = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        province: null,
        provinceCode: null,
        city: null,
        cityCode: null,
        county: null,
        countyCode: null,
        createDate: null,
        grade: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        proportion: 0,
        countyRegionId: null,
        cityRegionId: null,
        provinceRegionId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加代理信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      updateAgent({ id: row.id, status: 0 }).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.open = false;
        this.getList();
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAgent(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAgent(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    getDistrict() {
      district(0).then((res) => {
        this.provinceList = res.data;
      });
    },
    bindProvinceChange() {
      this.form.cityRegionId = null;
      this.form.countyRegionId = null;
      district(this.form.provinceRegionId).then((res) => {
        this.cityList = res.data;
      });
    },
    bindCityChange() {
      this.form.countyRegionId = null;
      district(this.form.cityRegionId).then((res) => {
        this.countyList = res.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  padding-right: 30px;
}
.dashboard-editor-container {
  padding: 12px;
  position: relative;

  .chart-wrapper {
    background: rgb(240, 242, 245);
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}
.card-panel-title {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
  text-align: center;
}
.card-panel-text {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
  text-align: center;
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
