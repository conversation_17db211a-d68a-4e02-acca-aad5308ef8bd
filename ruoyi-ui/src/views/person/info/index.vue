<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="用户手机号" prop="phonenumber">
        <el-input v-model="queryParams.phonenumber" placeholder="请输入用户手机号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="节点账号" prop="nodeAcc">
        <el-select v-model="queryParams.nodeAcc" placeholder="请选择是否节点账号" clearable>
          <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="身份证姓名" prop="identityName">
        <el-input v-model="queryParams.identityName" placeholder="请输入身份证姓名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="身份证号码" prop="identityNumber">
        <el-input v-model="queryParams.identityNumber" placeholder="请输入身份证号码" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="支付宝姓名" prop="aliPayName">
        <el-input v-model="queryParams.aliPayName" placeholder="请输入支付宝姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="支付宝账号" prop="aliPayAcc">
        <el-input v-model="queryParams.aliPayAcc" placeholder="请输入支付宝账号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="富星拼团购" prop="teamLeader">
        <el-select v-model="queryParams.teamLeader" placeholder="请选择" clearable>
          <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
          v-hasPermi="['person:info:import']">导入</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" border>
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.sysUser.userType == "01" ? "会员" : "商家" }}
        </template>
      </el-table-column>
      <el-table-column label="富星拼团购" align="center" prop="teamLeader"><template slot-scope="scope">
          <el-switch v-model="scope.row.teamLeader" :active-value="1" :inactive-value="0"
            @change="handleChange(scope.row, 'teamLeader')"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="有效用户" align="center" prop="effective">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.effective" :active-value="1" :inactive-value="0"
            @change="handleChange(scope.row, 'effective')"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="节点账号" align="center" prop="nodeAcc">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.nodeAcc" :active-value="1" :inactive-value="0"
            @change="handleChange(scope.row, 'nodeAcc')"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="积分" align="center" prop="integralTy" />
      <el-table-column label="贡献值" align="center" prop="integralGxz" />
      <el-table-column label="企业股" align="center" prop="integralJl" />
      <el-table-column label="权证" align="center" prop="integralSzqz" />
      <el-table-column label="权益值" align="center" prop="integralSj" />
      <el-table-column label="股权" align="center" prop="share" />
      <el-table-column label="承兑额度" align="center" prop="acceptorAmount" />
      <el-table-column label="可用额度" align="center" prop="consumeQuota.availableCredit" />
      <el-table-column label="已消耗额度" align="center" prop="consumeQuota.consumedCredit" />
      <el-table-column label="惠购值" align="center" prop="consumeQuota.preferentialCredit" />
      <el-table-column label="票证额度" align="center" prop="consumeQuota.exchangeTicketCredit" />
      <el-table-column label="数字权证额度" align="center" prop="consumeQuota.szqzCredit" />
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)">详情</el-button>

          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['person:info:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改人员基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="支付宝姓名" prop="aliPayName">
          <el-input v-model="form.aliPayName" placeholder="请输入支付宝姓名" />
        </el-form-item>
        <el-form-item label="支付宝账号" prop="aliPayAcc">
          <el-input v-model="form.aliPayAcc" placeholder="请输入支付宝账号" />
        </el-form-item>
        <el-form-item label="身份证姓名" prop="identityName">
          <el-input v-model="form.identityName" placeholder="请输入身份证姓名" />
        </el-form-item>
        <el-form-item label="身份证号码" prop="identityNumber">
          <el-input v-model="form.identityNumber" placeholder="请输入身份证号码" />
        </el-form-item>
        <el-form-item label="身份证性别" prop="identitySex">
          <el-select v-model="form.identitySex" placeholder="请选择身份证性别">
            <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="身份证有效期" prop="identityDate">
          <el-date-picker clearable v-model="form.identityDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择身份证有效期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 人员基本信息对话框 -->
    <el-dialog :title="title" :visible.sync="openInfo" width="70%" append-to-body>
      <el-form label-width="120px">
        <el-divider content-position="left">基础信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="手机号码:">
              {{ detailsFormSysUser.userName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="用户昵称:">
              {{ detailsFormSysUser.nickName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建时间:">
              {{ detailsForm.createTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="最后登录时间:">
              {{ detailsFormSysUser.loginDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="身份证姓名:">
              {{ detailsForm.identityName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证性别:">
              <dict-tag :options="dict.type.sys_user_sex" :value="detailsForm.identitySex" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证号码:">
              {{ detailsForm.identityNumber }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="身份证有效期:">
              {{ detailsForm.identityDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="实名认证时间:">
              {{ detailsForm.identityTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付宝姓名:">
              {{ detailsForm.aliPayName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付宝账号:">
              {{ detailsForm.aliPayAcc }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">节点信息</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="10富星卡冻结额:">
              <el-input-number v-model="detailsForm.nodeAccFreeze" :min="0" :step="1" :precision="0"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" v-hasPermi="['person:info:edit:nodeAccFreeze']" @click="
              handleNodeAccFreeze(
                detailsForm.userId,
                detailsForm.nodeAccFreeze
              )
              ">确认修改</el-button>
          </el-col>
        </el-row>
        <el-divider content-position="left">邀请人信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="手机号码:">
              {{ detailsFormInviterSysUser.userName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="用户昵称:">
              {{ detailsFormInviterSysUser.nickName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="用户类型:">
              {{
                detailsFormInviterSysUser.userType == "01"
                  ? "会员"
                  : detailsFormInviterSysUser.userType == "02"
                    ? "商家"
                    : ""
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">推广人统计信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="有效用户:">
              {{ detailsFormExtension.effective }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="推荐用户:">
              {{ detailsFormExtension.recommend }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="上月消费用户:">
              {{
                detailsFormExtension.lastMonthUser
              }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="本月消费用户:">
              {{
                detailsFormExtension.thisMonthUser
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="上月消费:">
              {{ detailsFormExtension.lastMonthAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="本月消费:">
              {{ detailsFormExtension.thisMonthAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="累计消费:">
              {{
                detailsFormExtension.totalAmount
              }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">推广人统计信息人员消费情况</el-divider>
        <div v-for="item in detailsFormExtension.peopleList" :key="item.userId"
          style="border: 1px solid #000;margin-top: 5px;border-radius: 10px;">
          <el-row>
            <el-col :span="6">
              <el-form-item label="手机号:">
                {{ item.phonenumber }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="加入时间:">
                {{ item.createTime }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="当前惠购值:">
                {{
                  item.preferentialCredit
                }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="上月消费:">
                {{ item.lastMonthAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="本月消费:">
                {{ item.thisMonthAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="累计消费:">
                {{
                  item.totalAmount
                }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" v-hasPermi="['person:info:edit:parentClear']"
          @click="handleClear(detailsForm.userId, 'parentClear')">清空邀请人</el-button>
        <el-button type="danger" v-hasPermi="['person:info:edit:identityClear']"
          @click="handleClear(detailsForm.userId, 'identityClear')">清空实名信息</el-button>
        <el-button type="warning" @click="handleUpdate(detailsForm.userId)"
          v-hasPermi="['person:info:edit:identity']">修改实名信息</el-button>
        <el-button @click="openInfo = false">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <!-- <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div> -->
          <span>仅允许导入xls、xlsx格式文件。</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  updateInfoType,
  updateInfoClear,
  getInfoExtensionUserSum,
  getInfoExtensionAmount,
} from "@/api/person/info";
import { getToken } from "@/utils/auth";
export default {
  name: "Info",
  dicts: ["sys_user_sex", "sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人员基本信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openInfo: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        identityName: null,
        identitySex: null,
        identityNumber: null,
        aliPayName: null,
        aliPayAcc: null,
        phonenumber: null,
        nodeAcc: null,
        teamLeader: null,
      },
      // 表单参数
      form: {},
      // 表单参数
      detailsForm: {
      },
      detailsFormSysUser: {},
      detailsFormInviterSysUser: {},
      detailsFormExtension: {
        effective: 0,
        recommend: 0,
        lastMonthUser: 0,
        thisMonthUser: 0,
        lastMonthAmount: 0,
        thisMonthAmount: 0,
        totalAmount: 0,
        peopleList: [],
      },
      // 表单校验
      rules: {
        identityName: [
          { required: true, message: "身份证姓名不能为空", trigger: "blur" },
        ],
        identitySex: [
          { required: true, message: "身份证性别不能为空", trigger: "change" },
        ],
        identityNumber: [
          { required: true, message: "身份证号码不能为空", trigger: "blur" },
        ],
        aliPayName: [
          { required: true, message: "支付宝姓名不能为空", trigger: "blur" },
        ],
        aliPayAcc: [
          { required: true, message: "支付宝账号不能为空", trigger: "blur" },
        ],
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/person/info/importData",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询人员基本信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        parentId: null,
        identityName: null,
        identitySex: null,
        identityNumber: null,
        identityDate: null,
        aliPayName: null,
        aliPayAcc: null,
        integralGxz: null,
        integralJl: null,
        integralSzqz: null,
        integralSj: null,
        integralTy: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人员基本信息";
    },
    handleInfo(row) {
      const userId = row.userId || this.ids;
      getInfo(userId).then((response) => {
        this.detailsForm = response.data;
        this.detailsFormSysUser = response.data.sysUser || {};
        this.detailsFormInviterSysUser = response.data.inviterSysUser || {};
        this.openInfo = true;
        this.title = "人员基本信息";
      });
      getInfoExtensionUserSum({ "userId": userId }).then((response) => {
        this.detailsFormExtension.effective = response.data?.effective ?? null;
        this.detailsFormExtension.recommend = response.data?.recommend ?? null;
      });
      getInfoExtensionAmount({ "userId": userId }).then((response) => {
        this.detailsFormExtension.lastMonthAmount = response.data?.lastMonthAmountSum ?? null;
        this.detailsFormExtension.thisMonthAmount = response.data?.monthAmountSum ?? null;
        this.detailsFormExtension.totalAmount = response.data?.amountSumAll ?? null;
        this.detailsFormExtension.lastMonthUser = response.data?.lastMonthPeople ?? null;
        this.detailsFormExtension.peopleList = response.data?.peopleList ?? null;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(userId) {
      this.reset();
      getInfo(userId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "人员基本信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.userId != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal
        .confirm("是否确认删除人员数据项？")
        .then(function () {
          return delInfo(userIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    // 人员信息修改
    handleChange(row, type) {
      let text = "";
      let data = {
        userId: row.userId,
        nodeAccFreeze: row.nodeAccFreeze,
      };
      if (type == "teamLeader") {
        data.teamLeader = row[type];
        text = row.teamLeader === 0 ? "取消富星拼团购" : "指定富星拼团购";
      } else if (type == "benefitRights") {
        data.benefitRights = row[type];
        text = row.benefitRights === 0 ? "取消富星超值购" : "指定富星超值购";
      } else if (type == "buyingMerchants") {
        data.buyingMerchants = row[type];
        text = row.buyingMerchants === 0 ? "取消富星品牌购" : "指定富星品牌购";
      } else if (type == "effective") {
        data.effective = row[type];
        text = row.effective === 0 ? "取消有效" : "指定有效";
      } else if (type == "nodeAcc") {
        data.nodeAcc = row[type];
        text =
          row.nodeAcc === 0
            ? "取消节点"
            : "冻结额度为<" + row.nodeAccFreeze + ">指定节点";
      }
      this.$modal
        .confirm("确认要" + text + "" + "用户吗？")
        .then(function () {
          return updateInfoType(data, type);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row[type] = row[type] === 0 ? 1 : 0;
        });
    },
    // 人员信息修改
    handleClear(userId, type) {
      let text = "";
      if (type == "parentClear") {
        text = "清空邀请人";
      } else if (type == "identityClear") {
        text = "清空实名信息";
      }
      this.$modal
        .confirm("确认要" + text + "" + "吗？")
        .then(function () {
          return updateInfoClear(type, userId);
        })
        .then(() => {
          this.openInfo = false;
          this.getList();
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () { });
    },
    handleNodeAccFreeze(userId, nodeAccFreeze) {
      this.$modal
        .confirm("确认要修改冻结10富星卡额度吗？")
        .then(function () {
          console.log(userId);
          console.log(nodeAccFreeze);
          let data = {
            userId: userId,
            nodeAccFreeze: nodeAccFreeze,
          };

          return updateInfoType(data, "nodeAccFreeze");
        })
        .then(() => {
          this.openInfo = false;
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(function () { });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
  },
};
</script>
