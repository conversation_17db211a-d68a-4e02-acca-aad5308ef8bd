<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品订单号" prop="commodityOrderId">
        <el-input
          v-model="queryParams.commodityOrderId"
          placeholder="请输入商品订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commissionList" border>
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column
        label="商品订单号"
        align="center"
        prop="commodityOrderId"
      />
      <el-table-column label="佣金额" align="center" prop="commission" />
      <el-table-column label="佣金名称" align="center" prop="name" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleStatistics(scope.row)"
            >查看佣金</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 佣金统计信息 -->
    <el-dialog :visible.sync="openStatistics" width="500px" append-to-body>
      <el-form label-width="120px">
        <el-form-item label="佣金总额:">
          {{ statisticsData.total }}
        </el-form-item>
        <el-form-item label="当前佣金:">
          {{ statisticsData.currentCommission }}
        </el-form-item>
        <el-form-item label="昨日收益:">
          {{ statisticsData.yesterday }}
        </el-form-item>
        <el-form-item label="累计提现:">
          {{ statisticsData.withdrawal }}
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCommission,
  statisticsCommission,
} from "@/api/person/commission/commission";

export default {
  name: "Commission",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 佣金信息表格数据
      commissionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openStatistics: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        commodityOrderId: null,
        userId: null,
        commission: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      statisticsData: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询佣金信息列表 */
    getList() {
      this.loading = true;
      listCommission(this.queryParams).then((response) => {
        this.commissionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        commodityOrderId: null,
        userId: null,
        commission: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 获取佣金统计 */
    handleStatistics(row) {
      statisticsCommission({ userId: row.userId }).then((res) => {
        this.statisticsData = res.data;
        this.openStatistics = true;
      });
    },
  },
};
</script>
