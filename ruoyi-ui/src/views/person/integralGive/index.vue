<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户账号" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="释放比例" prop="proportion">
        <el-input
          v-model="queryParams.proportion"
          placeholder="请输入释放比例"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['person:integralGive:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="integralGiveList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="用户主键" align="center" prop="userId" />
      <el-table-column label="用户账号" align="center" prop="phonenumber" />
      <el-table-column label="总赠送积分" align="center" prop="integral" />
      <el-table-column label="释放基数" align="center" prop="baseNumber" />
      <el-table-column label="释放比例" align="center" prop="proportion" />
      <el-table-column label="剩余积分" align="center" prop="balance" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['person:integralGive:edit']"
            >修改</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['person:integralGive:remove']"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改赠送积分对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="用户账号" prop="phonenumber">
          <el-input v-model="form.phonenumber" placeholder="请输入用户账号" />
        </el-form-item>
        <el-form-item label="总赠送积分" prop="integral">
          <el-input-number
            v-model="form.integral"
            placeholder="请输入总赠送积分"
            :min="0"
            :precision="2"
            :disabled="title == '修改赠送积分'"
          />
        </el-form-item>
        <el-form-item label="释放基数" prop="baseNumber">
          <el-input-number
            v-model="form.baseNumber"
            placeholder="请输入释放基数"
            :min="0"
            :precision="2"
          />
        </el-form-item>
        <el-form-item label="释放比例" prop="proportion">
          <el-input-number
            v-model="form.proportion"
            placeholder="请输入释放比例"
            :min="0"
            :max="1"
            :precision="5"
            :step="0.01"
          />
        </el-form-item>
        <el-form-item
          label="剩余积分"
          prop="balance"
          v-if="title == '修改赠送积分'"
        >
          <el-input-number
            v-model="form.balance"
            placeholder="请输入剩余积分"
            :min="0"
            :precision="2"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listIntegralGive,
  getIntegralGive,
  delIntegralGive,
  addIntegralGive,
  updateIntegralGive,
} from "@/api/person/integralGive";

export default {
  name: "IntegralGive",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 赠送积分表格数据
      integralGiveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        phonenumber: null,
        integral: null,
        proportion: null,
        balance: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        phonenumber: [
          { required: true, message: "用户账号不能为空", trigger: "blur" },
        ],
        integral: [
          { required: true, message: "总赠送积分不能为空", trigger: "blur" },
        ],
        proportion: [
          { required: true, message: "释放比例不能为空", trigger: "blur" },
        ],
        balance: [
          { required: true, message: "剩余积分不能为空", trigger: "blur" },
        ],
        baseNumber: [
          { required: true, message: "释放积分不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询赠送积分列表 */
    getList() {
      this.loading = true;
      listIntegralGive(this.queryParams).then((response) => {
        this.integralGiveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        phonenumber: null,
        integral: null,
        proportion: null,
        balance: null,
        baseNumber: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加赠送积分";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getIntegralGive(userId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改赠送积分";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.userId != null) {
            updateIntegralGive(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIntegralGive(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$modal
        .confirm("是否确认删除数据项？")
        .then(function () {
          return delIntegralGive(userIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "person/integralGive/export",
        {
          ...this.queryParams,
        },
        `integralGive_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
