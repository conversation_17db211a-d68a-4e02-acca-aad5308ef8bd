<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="截至日期" prop="deadline">
        <el-date-picker
          clearable
          v-model="queryParams.deadline"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择利润和股权分红总数截至日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="分红时间" prop="bonusTime">
        <el-date-picker
          clearable
          v-model="queryParams.bonusTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择分红时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['person:shareBonus:add']"
          >新增</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['person:shareBonus:export']"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="shareBonusList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="利润和股权分红总数截至日期"
        align="center"
        prop="deadline"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deadline, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分红时间" align="center" prop="bonusTime">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.bonusTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商城总利润" align="center" prop="profit" />
      <el-table-column
        label="富星卡回购总额"
        align="center"
        prop="consumeBack"
      />
      <el-table-column label="本次分红利润" align="center" prop="bonusAmount" />
      <el-table-column
        label="本次分红利润占比"
        align="center"
        prop="bonusProportion"
      />
      <el-table-column label="当前分红时总股权" align="center" prop="share" />
      <el-table-column
        label="当前分红时有效股权"
        align="center"
        prop="effectiveShare"
      />
      <el-table-column label="股权单价" align="center" prop="shareUnitPrice" />
      <el-table-column
        label="股权浮动单价"
        align="center"
        prop="shareUnitPriceFloat"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleDetailInfo(scope.row)"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['person:shareBonus:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['person:shareBonus:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改股权分红对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="截至日期" prop="deadline">
          <el-date-picker
            clearable
            v-model="form.deadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择利润和股权分红总数截至日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="分红时间" prop="bonusTime">
          <el-date-picker
            clearable
            v-model="form.bonusTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择分红时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="商城总利润" prop="profit">
          <el-input-number
            v-model="form.profit"
            :precision="4"
            :step="0.01"
            placeholder="请输入商城总利润"
          />
        </el-form-item>
        <el-form-item label="本次分红利润" prop="bonusAmount">
          <el-input-number
            v-model="form.bonusAmount"
            placeholder="请输入本次分红利润"
            :precision="4"
            :step="0.01"
          />
        </el-form-item>
        <el-form-item label="本次分红利润占比" prop="bonusProportion">
          <el-input-number
            :max="1"
            v-model="form.bonusProportion"
            placeholder="请输入本次分红利润占比"
            :precision="2"
            :step="0.01"
          />
        </el-form-item>
        <el-form-item label="股权单价" prop="shareUnitPrice">
          <el-input-number
            v-model="form.shareUnitPrice"
            placeholder="请输入股权单价"
            :precision="2"
            :step="0.01"
          />
        </el-form-item>
        <el-form-item label="股权浮动单价" prop="shareUnitPriceFloat">
          <el-input-number
            v-model="form.shareUnitPriceFloat"
            placeholder="请输入股权单价"
            :precision="2"
            :step="0.01"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--股权分红明细对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="openInfo"
      width="50%"
      append-to-body
    >
      <el-form
        :model="detailsQueryParams"
        ref="detailsQueryParams"
        size="small"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="用户账号" prop="params.phonenumber">
          <el-input
            v-model="detailsQueryParams.params.phonenumber"
            placeholder="请输入用户账号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="
              detailsQueryParams.pageNum = 1;
              getDetailsList(1);
            "
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="detailList" border>
        <el-table-column label="用户主键" align="center" prop="userId" />
        <el-table-column label="当前分红时总股权" align="center" prop="share" />
        <el-table-column
          label="当前分红时有效股权"
          align="center"
          prop="effectiveShare"
        />
        <el-table-column
          label="分红上月消费金额"
          align="center"
          prop="consumptionAmount"
        />
        <el-table-column
          label="当前分红时惠购值"
          align="center"
          prop="preferentialCredit"
        />
      </el-table>
      <pagination
        v-show="detailsTotal > 0"
        :total="detailsTotal"
        :page.sync="detailsQueryParams.pageNum"
        :limit.sync="detailsQueryParams.pageSize"
        @pagination="getDetailsList(1)"
      />
    </el-dialog>

    <!--股权分红明细对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="openInfoConsume"
      width="50%"
      append-to-body
    >
      <el-form
        :model="detailsQueryParams"
        ref="detailsQueryParams"
        size="small"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="用户账号" prop="params.phonenumber">
          <el-input
            v-model="detailsQueryParams.params.phonenumber"
            placeholder="请输入用户账号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="
              detailsQueryParams.pageNum = 1;
              getDetailsList(2);
            "
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="detailListConsume" border>
        <el-table-column label="用户主键" align="center" prop="userId" />
        <el-table-column
          label="富星卡编号"
          align="center"
          prop="consumeNumber"
        />

        <el-table-column
          label="富星卡金额"
          align="center"
          prop="consumeAmount"
        />
        <el-table-column
          label="兑换佣金"
          align="center"
          prop="commission"
        /><el-table-column label="操作时间" align="center" prop="createTime" />
      </el-table>
      <pagination
        v-show="detailsTotal > 0"
        :total="detailsTotal"
        :page.sync="detailsQueryParams.pageNum"
        :limit.sync="detailsQueryParams.pageSize"
        @pagination="getDetailsList(2)"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listShareBonus,
  getShareBonus,
  delShareBonus,
  addShareBonus,
  updateShareBonus,
  listBonusDetails,
  listBonusDetailsConsume,
} from "@/api/person/shareBonus";

export default {
  name: "ShareBonus",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //明细总条数
      detailsTotal: 0,
      // 股权分红表格数据
      shareBonusList: [],
      //分红明细数据
      detailList: [],
      //富星卡分红明细
      detailListConsume: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      openInfo: false,
      openInfoConsume: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deadline: null,
        bonusTime: null,
        profit: null,
        bonusAmount: null,
        bonusProportion: null,
        shareUnitPrice: null,
      },
      detailsQueryParams: {
        bonusId: null,
        params: { phonenumber: "" },
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deadline: [
          {
            required: true,
            message: "利润和股权分红总数截至日期不能为空",
            trigger: "blur",
          },
        ],
        bonusTime: [
          { required: true, message: "分红时间不能为空", trigger: "blur" },
        ],
        profit: [
          { required: true, message: "商城总利润不能为空", trigger: "blur" },
        ],
        bonusAmount: [
          { required: true, message: "本次分红利润不能为空", trigger: "blur" },
        ],
        bonusProportion: [
          {
            required: true,
            message: "本次分红利润占比不能为空",
            trigger: "blur",
          },
        ],
        shareUnitPrice: [
          { required: true, message: "股权单价不能为空", trigger: "blur" },
        ],
        shareUnitPriceFloat: [
          { required: true, message: "股权单价不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询股权分红列表 */
    getList() {
      this.loading = true;
      listShareBonus(this.queryParams).then((response) => {
        this.shareBonusList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询股权分红明细列表 */
    getDetailsList(type) {
      console.log(type);
      this.loading = true;
      if (type == 1) {
        listBonusDetails(this.detailsQueryParams).then((response) => {
          this.detailList = response.rows;
          this.detailsTotal = response.total;
          this.loading = false;
        });
      } else {
        listBonusDetailsConsume(this.detailsQueryParams).then((response) => {
          this.detailListConsume = response.rows;
          this.detailsTotal = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deadline: null,
        bonusTime: null,
        profit: null,
        bonusAmount: null,
        bonusProportion: null,
        shareUnitPrice: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        shareUnitPriceFloat: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加股权分红";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getShareBonus(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改股权分红";
      });
    },
    /** 详情按钮 */
    handleDetailInfo(row) {
      this.resetForm("detailsQueryParams");
      this.detailsQueryParams.bonusId = row.id;
      this.getDetailsList(row.type);
      if (row.type == 1) {
        this.openInfo = true;
        this.title = "股权分红明细";
      } else {
        this.openInfoConsume = true;
        this.title = "富星卡兑换明细";
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateShareBonus(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addShareBonus(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除股权分红编号为"' + ids + '"的数据项？')
        .then(function () {
          return delShareBonus(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "person/shareBonus/export",
        {
          ...this.queryParams,
        },
        `shareBonus_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
