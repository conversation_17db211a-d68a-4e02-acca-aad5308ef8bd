<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入买家账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="params.sellPhone">
        <el-input
          v-model="queryParams.params.sellPhone"
          placeholder="请输入卖家账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="supplierMapperOrderSn">
        <el-input
          v-model="queryParams.supplierMapperOrderSn"
          placeholder="请输入供应商订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="teamId">
        <el-input
          v-model="queryParams.teamId"
          placeholder="请输入团购编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="params.plateId">
        <el-select
          @keyup.enter.native="handleQuery"
          v-model="queryParams.params.plateId"
          placeholder="请选择商品板块"
          clearable
        >
          <el-option
            v-for="item in plateListOpt"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="commodityName">
        <el-input
          v-model="queryParams.commodityName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择订单状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_order_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="payType">
        <el-select
          v-model="queryParams.payType"
          placeholder="请选择支付类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.commodity_pay_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="payDateTime">
        <el-date-picker
          v-model="queryParams.params.payDateTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="支付时间开始日期"
          end-placeholder="支付时间结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: auto"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="" prop="deliveryDateTime">
        <el-date-picker
          clearable
          v-model="queryParams.deliveryDateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择发货时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="receivingDateTime">
        <el-date-picker
          clearable
          v-model="queryParams.receivingDateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择收货时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['commodity:order:export']"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" border>
      <el-table-column
        min-width="120"
        label="订单号"
        align="center"
        prop="id"
      />
      <el-table-column
        min-width="120"
        label="供应商订单号"
        align="center"
        prop="supplierMapperOrderSn"
      />
      <el-table-column label="账号" align="center" prop="createBy" />
      <el-table-column label="支付时间" align="center" prop="payDateTime" />
      <el-table-column label="板块名称" align="center" prop="plateName" />
      <el-table-column
        label="商品名称"
        align="center"
        prop="commodityName"
        min-width="200"
      />
      <el-table-column
        label="规格名称"
        align="center"
        prop="specificationsName"
        show-overflow-tooltip
        min-width="150"
      />
      <el-table-column label="商品单价" align="center" prop="unitPrice" />
      <el-table-column label="商品数量" align="center" prop="number" />
      <!-- <el-table-column label="运费" align="center" prop="freight" />
      <el-table-column label="税费" align="center" prop="taxesFees" /> -->
      <el-table-column label="总价金额" align="center" prop="totalAmount" />
      <el-table-column label="支付金额" align="center" prop="actualPayment" />
      <el-table-column label="订单状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.commodity_order_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        min-width="120px"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelivery(scope.row)"
            v-if="scope.row.status == 2"
            v-hasPermi="['commodity:delivery:add']"
            >发货</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateStatus(scope.row, 4)"
            v-if="scope.row.status == 3"
            v-hasPermi="['commodity:order:edit:admin']"
            >确认收货</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateStatus(scope.row, 5)"
            v-if="scope.row.status == 4"
            v-hasPermi="['commodity:order:edit']"
            >完成</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateStatus(scope.row, 7)"
            v-if="scope.row.status == 1"
            v-hasPermi="['commodity:order:edit']"
            >取消</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看商品订单信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="infoOpen"
      width="80%"
      append-to-body
    >
      <el-divider content-position="left">订单信息</el-divider>
      <el-form label-width="120px">
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="12">
            <el-form-item label="订单编号:">
              {{ formDetail.id }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="12" v-if="formDetail.teamId">
            <el-form-item label="拼团标识:">
              {{ formDetail.teamId }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="订单状态:">
              <dict-tag
                :options="dict.type.commodity_order_status"
                :value="formDetail.status"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="购买数量:">
              {{ formDetail.number }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="创建时间:">
              {{ formDetail.createTime }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="支付时间:">
              {{ formDetail.payDateTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="订单金额:">
              {{ formDetail.totalAmount }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="支付金额:">
              {{ formDetail.actualPayment }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="订单运费:">
              {{ formDetail.freight }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="订单税费:">
              {{ formDetail.taxesFees }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="支付类型:">
              <dict-tag
                :options="dict.type.commodity_pay_type"
                :value="formDetail.payType"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="发货时间:">
              {{ formDetail.deliveryDateTime }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="收货时间:">
              {{ formDetail.receivingDateTime }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="完成时间:">
              {{ formDetail.overDateTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">商品信息</el-divider>
      <el-form label-width="120px">
        <el-row>
          <el-col :xs="24" :sm="24" :lg="24">
            <el-form-item label="商品名称:">
              {{ formDetail.commodity.name }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="商品来源:">
              {{ formDetail.commodity.sourceType == 1 ? "商城内部" : "供应商" }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="所属板块:">
              {{ formDetail.commodity.plateName }}
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="标题图片:">
              <image-preview
                :src="formDetail.commodity.titleImgUrl"
                :width="50"
                :height="50"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="详情图片:">
              <image-preview
                :src="formDetail.commodity.detailsImgUrl"
                :width="50"
                :height="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">商品规格</el-divider>
      <el-form label-width="120px">
        <el-row>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="规格名称:">
              {{ formDetail.specifications.name }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="售卖价格:">
              {{ formDetail.specifications.price }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="供货价格:">
              {{ formDetail.specifications.supplyAmount }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="供货价浮动比例:">
              {{ formDetail.specifications.floatingRatio }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider content-position="left">地址信息</el-divider>
      <el-form label-width="120px">
        <el-row>
          <el-col :xs="24" :sm="24" :lg="8">
            <el-form-item label="收货姓名:">
              {{ formDetail.address.name }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <el-form-item label="手机号:">
              {{ formDetail.address.phone }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="8">
            <el-form-item label="详细地址:">
              {{ formDetail.address.province }}/{{ formDetail.address.city }}/{{
                formDetail.address.county
              }}/{{ formDetail.address.address }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div v-if="formDetail.invoice">
        <el-divider content-position="left">发票信息</el-divider>
        <el-form label-width="120px">
          <el-row>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="发票抬头:">
                {{ formDetail.invoice.invoiceHeader }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="单位税号:">
                {{ formDetail.invoice.taxId }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="注册地址:">
                {{ formDetail.invoice.registerAddress }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="注册电话:">
                {{ formDetail.invoice.registerPhone }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="开户银行:">
                {{ formDetail.invoice.openingBank }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="银行账号:">
                {{ formDetail.invoice.bankNumber }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-if="formDetail.orderInvoice">
        <el-divider content-position="left">开票信息</el-divider>
        <el-form label-width="120px">
          <el-row>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="开票时间:">
                {{ formDetail.orderInvoice.invoicingTime }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="发票图片:">
                <image-preview
                  :src="formDetail.orderInvoice.imgUrl"
                  :width="50"
                  :height="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-if="formDetail.evaluate">
        <el-divider content-position="left">评价信息</el-divider>
        <el-form label-width="120px">
          <el-row>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="商品评价:">
                <el-rate
                  style="margin-top: 10px"
                  v-model="formDetail.evaluate.commodityStarLevel"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                ></el-rate>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="商品描述评价:">
                <el-rate
                  style="margin-top: 10px"
                  v-model="formDetail.evaluate.describeStarLevel"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                ></el-rate>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="卖家服务:">
                <el-rate
                  style="margin-top: 10px"
                  v-model="formDetail.evaluate.sellerStarLevel"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                ></el-rate>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="物流服务:">
                <el-rate
                  style="margin-top: 10px"
                  v-model="formDetail.evaluate.logisticsStarLevel"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                ></el-rate>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="评价信息:">
                {{ formDetail.evaluate.message }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="8">
              <el-form-item label="评价图片:">
                <image-preview
                  :src="formDetail.evaluate.imgUrl"
                  :width="50"
                  :height="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="danger"
          @click="handleUpdateManageRefund(formDetail)"
          v-hasPermi="['commodity:refund:manageRefund']"
          v-if="
            formDetail.status != 1 &&
            formDetail.status != 6 &&
            formDetail.status != 7
          "
          :loading="loading"
          >仅发起退款</el-button
        >
        <el-button
          type="danger"
          @click="handleUpdateManagePointsRefund(formDetail)"
          v-hasPermi="['commodity:refund:manageRefund']"
          :loading="loading"
          v-if="
            formDetail.status != 1 &&
            formDetail.status != 6 &&
            formDetail.status != 7
          "
          >发起退款退积分退佣金</el-button
        >
        <el-button
          type="danger"
          v-hasPermi="['commodity:order:dingdongRestartPayment']"
          @click="handleDingdongRestartPayment(formDetail)"
          :loading="loading"
          v-if="formDetail.supplierMapperOrderSn != null"
          >供应链重新发起创建订单</el-button
        >
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        v-if="formDetail.commodity.plateId >= 9"
      >
        <el-button
          type="danger"
          @click="handleUpdateManageEnterpriseRefund(formDetail)"
          v-hasPermi="['commodity:refund:manageRefund']"
          :loading="loading"
          v-if="
            formDetail.status != 1 &&
            formDetail.status != 6 &&
            formDetail.status != 7 &&
            formDetail.commodity.plateId == 9
          "
          >发起企业购退款</el-button
        >
        <el-button
          type="danger"
          @click="handleUpdateManageEnterpriseRefund(formDetail)"
          v-hasPermi="['commodity:refund:manageRefund']"
          :loading="loading"
          v-if="
            formDetail.status != 1 &&
            formDetail.status != 6 &&
            formDetail.status != 7 &&
            formDetail.commodity.plateId == 10
          "
          >发起福利购退款</el-button
        >
      </div>
    </el-dialog>

    <!-- 添加订单物流信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="handleDeliveryOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formDelivery"
        :model="formDelivery"
        :rules="rules"
        label-width="110px"
      >
        <el-form-item label="收货人手机号" prop="deliveryPhone">
          <el-input
            v-model="formDelivery.deliveryPhone"
            placeholder="请输入收货人手机号"
          />
        </el-form-item>
        <el-form-item label="快递公司" prop="deliveryName">
          <el-input
            v-model="formDelivery.deliveryName"
            placeholder="请输入快递公司"
          />
        </el-form-item>
        <el-form-item label="快递单号" prop="deliveryNumber">
          <el-input
            v-model="formDelivery.deliveryNumber"
            placeholder="请输入快递单号"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormDelivery">确 定</el-button>
        <el-button @click="handleDeliveryOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listOrder,
  getOrder,
  updateOrder,
  dingdongRestartPayment,
} from "@/api/commodity/order";

import { addDelivery } from "@/api/commodity/delivery";

import { listPlate } from "@/api/commodity/plate";

import {
  manageRefund,
  manageePointsRefund,
  manageeEnterprisefund,
} from "@/api/commodity/refund";

export default {
  name: "Order",
  dicts: ["commodity_pay_type", "commodity_order_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品订单信息表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      editOpen: false,
      // 详情弹出层
      infoOpen: false,
      //板块查询参数
      plateListOpt: [],
      // 查询参数
      queryParams: {
        id: null,
        pageNum: 1,
        pageSize: 10,
        sellerUserId: null,
        buyerUserId: null,
        addressId: null,
        commodityId: null,
        commodityName: null,
        specificationsId: null,
        specificationsName: null,
        status: null,
        payType: null,
        payDateTime: null,
        deliveryDateTime: null,
        receivingDateTime: null,
        overDateTime: null,
        createBy: null,
        teamId: null,
        supplierMapperOrderSn: "",
        params: {
          payDateTime: [],
          startPayDateTime: null,
          endPayDateTime: null,
          plateId: null,
          sellPhone: null,
        },
      },
      // 表单参数
      form: {},
      // 表单详细信息
      formDetail: {
        commodity: {},
        specifications: {},
        address: {},
        invoice: {},
        evaluate: {},
        orderInvoice: {},
      },
      // 表单校验
      rules: {
        deliveryPhone: [
          { required: true, message: "收货人手机号不能为空", trigger: "blur" },
        ],
      },
      //物流
      handleDeliveryOpen: false,
      //物流表单参数
      formDelivery: {},
    };
  },
  created() {
    this.getList();
    this.getPlateOpt();
  },
  methods: {
    /** 查询商品订单信息列表 */
    getList() {
      console.log(this.queryParams.params.payDateTime);
      if (this.queryParams.params.payDateTime) {
        this.queryParams.params.startPayDateTime =
          this.queryParams.params.payDateTime[0];
        this.queryParams.params.endPayDateTime =
          this.queryParams.params.payDateTime[1];
      } else {
        this.queryParams.params.startPayDateTime = null;
        this.queryParams.params.endPayDateTime = null;
      }
      this.loading = true;
      listOrder(this.queryParams).then((response) => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.editOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sellerUserId: null,
        buyerUserId: null,
        addressId: null,
        commodityId: null,
        commodityName: null,
        specificationsId: null,
        specificationsName: null,
        specificationsImgUrl: null,
        unitPrice: null,
        number: null,
        freight: null,
        taxesFees: null,
        totalAmount: null,
        actualPayment: null,
        status: null,
        payType: null,
        invoiceId: null,
        payDateTime: null,
        deliveryDateTime: null,
        receivingDateTime: null,
        overDateTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    resetDelivery() {
      this.formDelivery = {
        id: null,
        userId: null,
        commodityOrderId: null,
        deliveryName: null,
        deliveryPhone: null,
        deliveryNumber: null,
        type: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.params.payDateTime = [];
      this.handleQuery();
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      const id = row.id || this.ids;
      getOrder(id).then((response) => {
        this.formDetail = response.data;
        this.infoOpen = true;
        this.title = "商品订单详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdateStatus(row, status) {
      const id = row.id || this.ids;
      let url = "";
      if (status == 4) {
        url = "receiving";
      } else if (status == 5) {
        url = "complete";
      } else if (status == 7) {
        url = "cancellation";
      }
      updateOrder({}, url, id).then((res) => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      });
    },
    //物流发货
    handleDelivery(row) {
      this.resetDelivery();
      this.formDelivery.commodityOrderId = row.id;
      this.handleDeliveryOpen = true;
      this.title = "添加订单物流信息";
    },
    /** 提交按钮 */
    submitFormDelivery() {
      this.$refs["formDelivery"].validate((valid) => {
        if (valid) {
          addDelivery(this.formDelivery).then((response) => {
            this.$modal.msgSuccess("发货成功");
            this.handleDeliveryOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "commodity/order/export",
        {
          ...this.queryParams,
        },
        `订单信息_${new Date().getTime()}.xlsx`
      );
    },
    //查询板块信息
    getPlateOpt() {
      listPlate().then((response) => {
        this.plateListOpt = response.rows;
      });
    },
    //发起退款-仅退款
    handleUpdateManageRefund(row) {
      this.loading = true;
      manageRefund(row).then((response) => {
        this.$modal.msgSuccess("退款成功");
        this.infoOpen = false;
        this.loading = false;
        this.getList();
      });
    },
    //发起退款-退款退积分退佣金
    handleUpdateManagePointsRefund(row) {
      this.loading = true;
      manageePointsRefund(row).then((response) => {
        this.$modal.msgSuccess("退款成功");
        this.infoOpen = false;
        this.loading = false;
        this.getList();
      });
    },
    handleUpdateManageEnterpriseRefund(row) {
      this.loading = true;
      manageeEnterprisefund(row).then((response) => {
        this.$modal.msgSuccess("退款成功");
        this.infoOpen = false;
        this.loading = false;
        this.getList();
      });
    },
    handleDingdongRestartPayment(row) {
      this.loading = true;
      dingdongRestartPayment(row.id).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.loading = false;
        this.getList();
      });
    },
  },
};
</script>
