<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="商品板块" prop="plateId">
        <el-select
          v-model="queryParams.plateId"
          placeholder="请选择商品板块"
          clearable
        >
          <el-option
            v-for="dict in plateOptionList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商家账号" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入商家账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="商品状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择商品状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.community_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <div style="text-align: center; color: red; padding: 5px">
      <div style="font-size: 35px">FBI WARNING:</div>
      <div style="font-size: 25px">
        请勿对标有置顶的商品进行拖动排序，如需对置顶商品排序，请先取消置顶后操作
      </div>
    </div>
    <div style="height: 80%">
      <draggable v-model="infoList" @start="draggableStart" @end="draggableEnd">
        <el-col :span="6" v-for="item in infoList" :key="item.id">
          <el-card style="" class="logo-container">
            <img
              v-if="item.topUpTime"
              src="@/assets/images/top.jpg"
              alt="置顶"
              class="logo"
            />
            <div style="text-align: center">
              <image-preview
                :src="item.titleImgUrl"
                :height="100"
                :width="100"
              />
            </div>
            <div style="padding: 14px">
              <span> {{ item.name }}</span>
            </div>
          </el-card>
        </el-col>
      </draggable>
    </div>
  </div>
</template>

<script>
import { listInfo, sortNumber } from "@/api/commodity/info";
import { listPlate } from "@/api/commodity/plate";
import draggable from "vuedraggable";
import { Loading } from "element-ui";
export default {
  dicts: ["community_status"],
  components: {
    draggable,
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品管理表格数据
      infoList: [],
      //初始数据
      infoInitialList: [],
      //板块列表
      plateOptionList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        plateId: null,
        createBy: null,
        status: "2",
      },
      drag: false,
      downloadLoadingInstance: null,
    };
  },
  created() {
    this.getList();
    this.getPlateList();
  },
  methods: {
    /** 查询商品管理列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.infoInitialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 查询板块列表
    getPlateList() {
      listPlate().then((response) => {
        this.plateOptionList = response.rows;
        this.plateOptionList.unshift({ id: 0, name: "普通商品" });
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    draggableStart(event) {},
    draggableEnd(event) {
      let newIndex = event.newIndex;
      let oldIndex = event.oldIndex;
      if (newIndex == oldIndex) {
        return;
      }
      let oldData = this.infoInitialList[oldIndex];
      let newData = this.infoInitialList[newIndex];
      this.handleUpdateSortNumber(
        oldData.id,
        oldData.sortNumber,
        newData.sortNumber
      );
    },
    /** 排序操作 */
    handleUpdateSortNumber(id, oldIndex, newIndex) {
      this.downloadLoadingInstance = Loading.service({
        text: "正在排序，请稍候",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = {
        id: id,
        oldIndex: oldIndex,
        newIndex: newIndex,
      };
      sortNumber(data)
        .then((response) => {
          this.downloadLoadingInstance.close();
          this.getList();
        })
        .catch((r) => {
          this.downloadLoadingInstance.close();
        });
    },
  },
};
</script>
<style>
.logo-container {
  position: relative; /* 设定为相对定位，以便其子元素能进行绝对定位 */
  padding: 10px 0px;
  margin: 10px;
  height: 200px;
}

.logo-container .logo {
  position: absolute; /* 绝对定位 */
  top: 0px; /* 距离容器顶部10px */
  right: 0px; /* 距离容器右侧10px */
  width: 80px; /* logo图片宽度 */
  height: auto; /* 高度自适应，保持图片比例 */
}
</style>
