<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="50px"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['commodity:plate:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="plateList"
      @selection-change="handleSelectionChange"
      border
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column label="名称" prop="name" />
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="图标" align="center" prop="imgUrl">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="页面地址" align="center" prop="pageUrl" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleAdd(scope.row)"
            v-if="scope.row.parentId == 0 && scope.row.id == 1"
            v-hasPermi="['commodity:plate:add']"
            >新增</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleBuy(scope.row)"
            v-if="scope.row.parentId"
            v-hasPermi="['commodity:plate:add']"
            >设置购买规则</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['commodity:plate:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="scope.row.parentId"
            v-hasPermi="['commodity:plate:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改商品板块对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="板块名称" prop="name" v-if="!buy">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="板块排序" prop="sort" v-if="!buy">
          <el-input-number
            :precision="0"
            :min="0"
            v-model="form.sort"
            placeholder="请输入排序"
          />
        </el-form-item>
        <el-form-item label="板块图标" prop="imgUrl" v-if="!buy">
          <image-upload v-model="form.imgUrl" />
        </el-form-item>
        <el-form-item label="页面地址" prop="pageUrl" v-if="!buy">
          <el-input v-model="form.pageUrl" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="购买面值" prop="consumeAmount" v-if="buy">
          <el-input-number v-model="form.consumeAmount" :min="0" />
        </el-form-item>
        <el-form-item label="最多抵押数量" prop="buyNumber" v-if="buy">
          <el-input-number v-model="form.buyNumber" :min="0" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPlate,
  getPlate,
  delPlate,
  addPlate,
  updatePlate,
} from "@/api/commodity/plate";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Plate",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品板块表格数据
      plateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 购买规则修改
      buy: false,
      // 查询参数
      queryParams: {
        name: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        sort: [
          { required: true, message: "显示排序不能为空", trigger: "blur" },
        ],
        consumeAmount: [
          { required: true, message: "购买面值不能为空", trigger: "blur" },
        ],
        buyNumber: [
          { required: true, message: "最多抵押数量不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商品板块列表 */
    getList() {
      this.loading = true;
      listPlate(this.queryParams).then((response) => {
        this.plateList = this.handleTree(response.rows, "id");
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        parentId: null,
        name: null,
        sort: null,
        imgUrl: null,
        pageUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        consumeAmount: null,
        buyNumber: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row) {
        this.form.parentId = row.id;
      }
      this.open = true;
      this.title = "添加商品板块";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.buy = false;
      const id = row.id || this.ids;
      getPlate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改商品板块";
      });
    },
    handleBuy(row) {
      this.reset();
      this.buy = true;
      const id = row.id || this.ids;
      getPlate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "购买规则";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePlate(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlate(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除商品板块数据项？")
        .then(function () {
          return delPlate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
  },
};
</script>
