<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="团长手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入团长手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称 " prop="params.commodityName">
        <el-input
          v-model="queryParams.params.commodityName"
          placeholder="请输入商品名称 "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拼团状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择拼团状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_commodity_teamwork_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="拼团链接" prop="addressLink">
        <el-input
          v-model="queryParams.addressLink"
          placeholder="请输入商品名称 "
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="enterpriseList" border>
      <el-table-column label="拼团总数" align="center" prop="groupSize" />
      <el-table-column
        label="当前数量"
        align="center"
        prop="currentGroupSize"
      />
      <el-table-column label="发起拼团时间" align="center" prop="startTime">
      </el-table-column>
      <el-table-column label="结束拼团时间" align="center" prop="endTime">
      </el-table-column>
      <el-table-column label="拼团状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commodity_teamwork_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="地址链接" align="center" prop="addressLink" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-if="scope.row.status == 0 && scope.row.status != 3"
            v-hasPermi="['teamwork:group:edit:cancel']"
            :loading="loading"
            >取消拼团</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改拼团购拼团信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form label-width="80px">
        <el-divider content-position="left">团长信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="团长账号:">
              {{ sysUser.phonenumber }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="团长昵称:">
              {{ sysUser.nickName }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型:">
              {{ sysUser.userType == "01" ? "会员" : "商家" }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">拼团信息</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="当前数量:">
              {{ form.currentGroupSize }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址链接:">
              {{ form.addressLink }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发起时间:">
              {{ form.startTime }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
              <el-button
                type="primary"
                style="margin-left: 20px"
                @click="handleUpdateEndTime"
                v-if="form.status == 0"
                v-hasPermi="['teamwork:group:edit']"
                >确认修改结束时间</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">地址信息</el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="收货姓名:">
              {{ address.name }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="手机号码:">
              {{ address.phone }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="省市区县:">
              {{ address.province }}/{{ address.city }}/{{ address.county }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="详细地址:">
              {{ address.address }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">商品信息</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="商品名称:">
              {{ commodity.name }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标题图片:">
              <image-preview
                :src="commodity.titleImgUrl"
                :width="50"
                :height="50"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="详情图片:">
              <image-preview
                :src="commodity.detailsImgUrl"
                :width="50"
                :height="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="商品详情:">
              {{ commodity.details }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供货价格:">
              {{ commodity.supplyAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="零售价格:">
              {{ commodity.retailAmount }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-for="(commodityOrder, index) in commodityOrderList"
          :key="index"
        >
          <el-divider content-position="left">订单信息</el-divider>
          <el-row>
            <el-col :span="6">
              <el-form-item label="订单编号:">
                {{ commodityOrder.id }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="购买数量:">
                {{ commodityOrder.number }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创建时间:">
                {{ commodityOrder.createTime }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="支付时间:">
                {{ commodityOrder.payDateTime }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="订单金额:">
                {{ commodityOrder.totalAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="支付金额:">
                {{ commodityOrder.actualPayment }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单运费:">
                {{ commodityOrder.freight }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单税费:">
                {{ commodityOrder.taxesFees }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="支付类型:">
                <dict-tag
                  :options="dict.type.commodity_pay_type"
                  :value="commodityOrder.payType"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发货时间:">
                {{ commodityOrder.deliveryDateTime }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="收货时间:">
                {{ commodityOrder.receivingDateTime }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单状态:">
                <dict-tag
                  :options="dict.type.commodity_order_status"
                  :value="commodityOrder.status"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGroup,
  getGroup,
  teamworkCancel,
  updateGroup,
} from "@/api/commodity/teamwork/group";
import { listOrder } from "@/api/commodity/order";
export default {
  name: "Group",
  dicts: [
    "sxsc_commodity_teamwork_status",
    "commodity_pay_type",
    "commodity_order_status",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 拼团购拼团信息表格数据
      enterpriseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        status: null,
        addressLink: null,
        params: {
          phonenumber: null,
          commodityName: null,
        },
      },
      // 表单参数
      form: {},
      address: {},
      commodity: {},
      commodityOrderList: {},
      sysUser: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询拼团购拼团信息列表 */
    getList() {
      this.loading = true;
      listGroup(this.queryParams).then((response) => {
        this.enterpriseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        commodityId: null,
        addressId: null,
        groupSize: null,
        startTime: null,
        endTime: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        addressLink: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleInfo(row) {
      const id = row.id || this.ids;
      this.commodityOrderList = [];
      listOrder({ teamId: id }).then((res) => {
        this.commodityOrderList = res.rows;
      });
      getGroup(id).then((response) => {
        this.form = response.data;
        this.address = response.data.address || {};
        this.commodity = response.data.commodity || {};
        this.sysUser = response.data.sysUser || {};
        this.open = true;
        this.title = "拼团购拼团信息";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      teamworkCancel(row.id).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.loading = false;
        this.getList();
      });
    },
    handleUpdateEndTime() {
      updateGroup({ id: this.form.id, endTime: this.form.endTime }).then(
        (response) => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.getList();
        }
      );
    },
  },
};
</script>
