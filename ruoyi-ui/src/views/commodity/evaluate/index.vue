<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="商品订单标识" prop="commodityOrderId">
        <el-input
          v-model="queryParams.commodityOrderId"
          placeholder="请输入商品订单标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入评价用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="evaluateList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="商品订单标识"
        align="center"
        prop="commodityOrderId"
      />
      <el-table-column label="商品名称" align="center" prop="commodityOrder.commodityName" />
      <el-table-column label="商品评价" align="center" prop="commodityStarLevel" />
      <el-table-column label="商品描述评价" align="center" prop="describeStarLevel" />
      <el-table-column label="卖家家服务" align="center" prop="sellerStarLevel" />
      <el-table-column label="物流服务" align="center" prop="logisticsStarLevel" />
      <el-table-column label="评价信息" align="center" prop="message" show-overflow-tooltip/>
      <el-table-column label="评价图片信息" align="center" prop="imgUrl">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="评价用户姓名" align="center" prop="sysUser.nickName" />
      <el-table-column label="评价用户手机号" align="center" prop="sysUser.phonenumber" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['commodity:evaluate:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['commodity:evaluate:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品评价" prop="commodityStarLevel">
          <el-input-number
            :step="1"
            :min="1"
            :max="5"
            v-model="form.commodityStarLevel"
            placeholder="请输入评分"
          />
        </el-form-item>
        <el-form-item label="描述评价" prop="describeStarLevel">
          <el-input-number
            :step="1"
            :min="1"
            :max="5"
            v-model="form.describeStarLevel"
            placeholder="请输入评分"
          />
        </el-form-item>
        <el-form-item label="卖家服务" prop="sellerStarLevel">
          <el-input-number
            :step="1"
            :min="1"
            :max="5"
            v-model="form.sellerStarLevel"
            placeholder="请输入评分"
          />
        </el-form-item>
        <el-form-item label="物流服务" prop="logisticsStarLevel">
          <el-input-number
            :step="1"
            :min="1"
            :max="5"
            v-model="form.logisticsStarLevel"
            placeholder="请输入评分"
          />
        </el-form-item>
        <el-form-item label="评价信息" prop="message">
          <el-input
            v-model="form.message"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="评价图片" prop="imgUrl">
          <image-upload v-model="form.imgUrl" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEvaluate,
  getEvaluate,
  delEvaluate,
  addEvaluate,
  updateEvaluate,
} from "@/api/commodity/evaluate";

export default {
  name: "Evaluate",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价信息表格数据
      evaluateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        commodityId: null,
        commodityOrderId: null,
        enterpriseUserId: null,
        message: null,
        userId: null,
        userName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        commodityId: [
          { required: true, message: "商品主键不能为空", trigger: "blur" },
        ],
        commodityOrderId: [
          { required: true, message: "商品订单主键不能为空", trigger: "blur" },
        ],
        commodityStarLevel: [
          { required: true, message: "评分不能为空", trigger: "blur" },
        ],
        describeStarLevel: [
          { required: true, message: "评分不能为空", trigger: "blur" },
        ],
        sellerStarLevel: [
          { required: true, message: "评分不能为空", trigger: "blur" },
        ],
        logisticsStarLevel: [
          { required: true, message: "评分不能为空", trigger: "blur" },
        ],
        message: [
          { required: true, message: "评价信息不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询评价信息列表 */
    getList() {
      this.loading = true;
      listEvaluate(this.queryParams).then((response) => {
        this.evaluateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        commodityId: null,
        commodityOrderId: null,
        enterpriseUserId: null,
        commodityStarLevel: null,
        describeStarLevel: null,
        sellerStarLevel: null,
        logisticsStarLevel: null,
        message: null,
        imgUrl: null,
        userId: null,
        userName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评价信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEvaluate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改评价信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEvaluate(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEvaluate(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除评价信息？")
        .then(function () {
          return delEvaluate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "commodity/evaluate/export",
        {
          ...this.queryParams,
        },
        `evaluate_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
