<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="团长手机号" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入团长手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拼团状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择拼团状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_commodity_teamwork_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品订单主键" prop="commodityOrderId">
        <el-input
          v-model="queryParams.commodityOrderId"
          placeholder="请输入商品订单主键"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="groupBuyingList" border>
      <el-table-column label="团购标识" align="center" prop="id" />
      <el-table-column
        label="商品订单主键"
        align="center"
        prop="commodityOrderId"
      />
      <el-table-column label="拼团状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_commodity_teamwork_status"
            :value="scope.row.status"
          />
        </template>
      </el-table-column>
      <el-table-column label="发起时间" align="center" prop="createTime" />
      <el-table-column
        label="当前订单数量"
        align="center"
        prop="currentCount"
      />
      <el-table-column
        label="当前订单总额"
        align="center"
        prop="currentTurnover"
      />
      <el-table-column label="完成时间" align="center" prop="updateTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleInfo(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改品牌购拼团信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form label-width="120px">
        <el-divider content-position="left">团长信息</el-divider>
        <el-row>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="团长账号:">
              {{ sysUser.phonenumber }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="团长昵称:">
              {{ sysUser.nickName }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="6">
            <el-form-item label="类型:">
              {{ sysUser.userType == "01" ? "会员" : "商家" }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">拼团信息</el-divider>
        <el-row>
          <el-col :xs="24" :sm="24" :lg="12">
            <el-form-item label="当前数量:">
              {{ form.currentCount }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="12">
            <el-form-item label="当前营业额:">
              {{ form.currentTurnover }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :sm="24" :lg="12">
            <el-form-item label="发起时间:">
              {{ form.createTime }}
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :lg="12">
            <el-form-item label="完成时间:">
              {{ form.updateTime }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          v-for="(commodityOrder, index) in commodityOrderList"
          :key="index"
        >
          <el-divider content-position="left">订单信息</el-divider>
          <el-row>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="订单编号:">
                {{ commodityOrder.id }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="购买数量:">
                {{ commodityOrder.number }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="创建时间:">
                {{ commodityOrder.createTime }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="支付时间:">
                {{ commodityOrder.payDateTime }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="订单金额:">
                {{ commodityOrder.totalAmount }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="支付金额:">
                {{ commodityOrder.actualPayment }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="订单运费:">
                {{ commodityOrder.freight }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="订单税费:">
                {{ commodityOrder.taxesFees }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="支付类型:">
                <dict-tag
                  :options="dict.type.commodity_pay_type"
                  :value="commodityOrder.payType"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="发货时间:">
                {{ commodityOrder.deliveryDateTime }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="收货时间:">
                {{ commodityOrder.receivingDateTime }}
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :lg="6">
              <el-form-item label="订单状态:">
                <dict-tag
                  :options="dict.type.commodity_order_status"
                  :value="commodityOrder.status"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGroupBuying,
  getGroupBuying,
  delGroupBuying,
  addGroupBuying,
  updateGroupBuying,
} from "@/api/commodity/groupBuying";
import { listOrder } from "@/api/commodity/order";
export default {
  name: "GroupBuying",
  dicts: [
    "sxsc_commodity_teamwork_status",
    "commodity_order_status",
    "commodity_pay_type",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 富星拼团购表格数据
      groupBuyingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        createBy: null,
        commodityOrderId: null,
        status: null,
        currentCount: null,
        currentTurnover: null,
      },
      // 表单参数
      form: {},
      commodityOrderList: [],
      sysUser: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询富星拼团购列表 */
    getList() {
      this.loading = true;
      listGroupBuying(this.queryParams).then((response) => {
        this.groupBuyingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        commodityOrderId: null,
        status: null,
        currentCount: null,
        currentTurnover: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加富星拼团购";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGroupBuying(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改富星拼团购";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateGroupBuying(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGroupBuying(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除富星拼团购编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGroupBuying(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 查看详情 */
    handleInfo(row) {
      const id = row.id || this.ids;
      this.commodityOrderList = [];
      listOrder({ teamId: id }).then((res) => {
        this.commodityOrderList = res.rows;
      });
      getGroupBuying(id).then((response) => {
        this.form = response.data;
        this.sysUser = response.data.sysUser || {};
        this.open = true;
        this.title = "拼团购信息";
      });
    },
  },
};
</script>
