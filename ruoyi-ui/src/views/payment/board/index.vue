<template>
  <div class="dashboard-editor-container">
    <div style="text-align: center">
      <span style="font-size: 36px">商品销售总览</span>
    </div>

    <div style="text-align: right">
      <span style="font-size: 18px">
        {{ parseTime(dateTime) }}
      </span>
    </div>
    <div style="padding: 0; margin-bottom: 16px">
      <el-date-picker
        v-model="ali.aliMonth"
        type="month"
        placeholder="选择月"
        value-format="yyyy-MM"
        @change="getAliPayAmount"
      >
      </el-date-picker>
      <el-row :gutter="32" style="margin-top: 10px">
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">总销售额</div>
            <div class="card-panel-text" style="color: red">
              {{ ali.aliPaySum }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">总支出额</div>
            <div class="card-panel-text" style="color: green">
              {{
                (
                  ali.aliPayWithdrawalSum + ali.orderNewcomerSupplyAmountSum
                ).toFixed(2)
              }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">总退款额</div>
            <div class="card-panel-text" style="color: black">
              {{ ali.aliPayRefundSum }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">新人特惠</div>
            <div class="card-panel-text" style="color: #40c9c6">
              {{ ali.orderNewcomerSupplyAmountSum }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">货款总额</div>
            <div class="card-panel-text" style="color: #40c9c6">
              {{
                (
                  ali.aliPayWithdrawalSum - ali.aliPayWithdrawalCommissionSum
                ).toFixed(2)
              }}
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :lg="4">
          <div class="chart-wrapper">
            <div class="card-panel-title">佣金总额</div>
            <div class="card-panel-text" style="color: #40c9c6">
              {{ ali.aliPayWithdrawalCommissionSum }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <span class="title">平台订单总数量</span>
      <el-date-picker
        v-model="orderCountDate"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getOrderCountLineChange"
        style="width: auto"
      >
      </el-date-picker>

      <line-chart :chart-data="orderCountLineData" />
    </el-row>
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <span class="title">各板块订单数量</span>
      <el-date-picker
        v-model="orderCountDate"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getOrderCountLineChange"
        style="width: auto"
      >
      </el-date-picker>
      <line-chart :chart-data="orderPayPlateLineData" />
    </el-row>
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <span class="title">平台订单营业额</span>
      <el-date-picker
        v-model="orderPayDate"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getOrderPayLineChange"
        style="width: auto"
      >
      </el-date-picker>
      <line-chart :chart-data="orderPayLineData" />
    </el-row>
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <span class="title">平台各板块利润</span>
      <el-date-picker
        v-model="orderPayDate"
        type="daterange"
        value-format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="getOrderPayLineChange"
        style="width: auto"
      >
      </el-date-picker>
      <line-chart :chart-data="orderPayPlateProfitAmountLineData" />
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="6">
        <div class="chart-wrapper">
          <div class="card-panel-title">广告昨日预估收益</div>
          <div class="card-panel-text" style="color: #40c9c6">
            {{ advertisementData.yestoday }}
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6">
        <div class="chart-wrapper">
          <div class="card-panel-title">广告七日预估内收益</div>
          <div class="card-panel-text" style="color: #40c9c6">
            {{ advertisementData.seven }}
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6">
        <div class="chart-wrapper">
          <div class="card-panel-title">广告当月预估收益</div>
          <div class="card-panel-text" style="color: #40c9c6">
            {{ advertisementData.month }}
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="6">
        <div class="chart-wrapper">
          <div class="card-panel-title">广告上个月预估收益</div>
          <div class="card-panel-text" style="color: #40c9c6">
            {{ advertisementData.last_month }}
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <line-chart :chart-data="advertisementLineData" />
    </el-row>
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <pie-chart :seriesData="pieSalesSeriesData" />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <pie-chart :seriesData="pieProfitSeriesData" />
        </div>
      </el-col>
      <!-- <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import LineChart from "@/views/dashboard/LineChart";
import PieChart from "@/views/dashboard/PieChart";
import BarChart from "@/views/dashboard/BarChart";
import {
  orderCountLine,
  orderPayLine,
  orderProfitAmount,
  orderSalesAmount,
  advertisement,
  orderPlateCountLine,
  orderPlateCountProfitAmountLine,
  aliPayAmount,
} from "@/api/seting/board";

export default {
  name: "Index",
  components: {
    LineChart,
    PieChart,
    BarChart,
  },
  data() {
    return {
      dateTime: new Date(),
      orderCountDate: [],
      orderCountLineData: {},
      orderPayDate: [],
      orderPayLineData: {},
      orderPayPlateLineData: {},
      orderPayPlateProfitAmountLineData: {},
      advertisementLineData: {},
      advertisementData: {
        yestoday: 0,
        seven: 0,
        month: 0,
        last_month: 0,
      },
      ali: {
        aliMonth: "",
        aliPaySum: 0,
        aliPayWithdrawalCommissionSum: 0,
        aliPayWithdrawalSum: 0,
        aliPayRefundSum: 0,
        orderNewcomerSupplyAmountSum: 0,
      },
      pieProfitSeriesData: {},
      pieSalesSeriesData: {},
      timer: null,
      timerTime: null,
    };
  },
  created() {
    this.init();
    // 创建定时任务
    this.timer = setInterval(() => {
      this.init();
    }, 120000);
    this.timerTime = setInterval(() => {
      this.dateTime = new Date();
    }, 1000);
  },
  beforeDestroy() {
    // 清理定时任务
    clearInterval(this.timer);
    clearInterval(this.timerTime);
  },
  methods: {
    init() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      this.orderCountDate = [
        this.parseTime(start, "{y}-{m}-{d}"),
        this.parseTime(end, "{y}-{m}-{d}"),
      ];
      this.orderPayDate = [
        this.parseTime(start, "{y}-{m}-{d}"),
        this.parseTime(end, "{y}-{m}-{d}"),
      ];
      this.getOrderCountLine();
      this.orderPayLine();
      this.orderProfit();
      this.orderSales();
      this.getAdvertisement();
      this.getOrderPlateCountLine();
      this.getOrderPayPlateProfitAmountLine();
      this.getAliPayAmount();
    },
    //获取订单个数数据
    getOrderCountLine() {
      let data = {
        startDate: this.orderCountDate[0],
        endDate: this.orderCountDate[1],
      };
      orderCountLine(data).then((res) => {
        this.orderCountLineData = {
          xData: res.data.xData,
          legendData: ["订单总数", "实际支付总数"],
          firstData: {
            name: "订单总数",
            data: res.data.orderCount,
          },
          secondData: {
            name: "实际支付总数",
            data: res.data.orderPayCount,
          },
        };
      });
    },
    //获取订单板块订单个数数据
    getOrderPlateCountLine() {
      let data = {
        startDate: this.orderCountDate[0],
        endDate: this.orderCountDate[1],
      };
      orderPlateCountLine(data).then((res) => {
        this.orderPayPlateLineData = {
          xData: res.data.xData,
          legendData: ["普通订单", "超值购", "品牌购", "拼团购", "新人特惠"],
          firstData: {
            name: "普通订单",
            data: res.data.orderCountOrdinary,
          },
          secondData: {
            name: "超值购",
            data: res.data.orderCountBenefitRights,
          },
          thirdData: {
            name: "品牌购",
            data: res.data.orderCountBuyingMerchants,
          },
          fourthData: {
            name: "拼团购",
            data: res.data.orderPayCountTeamLeader,
          },
          fifthData: {
            name: "新人特惠",
            data: res.data.orderPayCountEmployee,
          },
        };
      });
    },
    getOrderPayPlateProfitAmountLine() {
      let data = {
        startDate: this.orderPayDate[0],
        endDate: this.orderPayDate[1],
      };
      orderPlateCountProfitAmountLine(data).then((res) => {
        this.orderPayPlateProfitAmountLineData = {
          xData: res.data.xData,
          legendData: [
            "普通订单利润",
            "超值购利润",
            "品牌购利润",
            "拼团购利润",
            "新人特惠利润",
          ],
          firstData: {
            name: "普通订单利润",
            data: res.data.orderProfitAmountOrdinary,
          },
          secondData: {
            name: "超值购利润",
            data: res.data.orderProfitAmountBenefitRights,
          },
          thirdData: {
            name: "品牌购利润",
            data: res.data.orderProfitAmountBuyingMerchants,
          },
          fourthData: {
            name: "拼团购利润",
            data: res.data.orderPayProfitAmountTeamLeader,
          },
          fifthData: {
            name: "新人特惠利润",
            data: res.data.orderPayProfitAmountEmployee,
          },
        };
      });
    },
    getOrderCountLineChange() {
      this.getOrderCountLine();
      this.getOrderPlateCountLine();
    },
    getOrderPayLineChange() {
      this.orderPayLine();
      this.getOrderPayPlateProfitAmountLine();
    },
    //获取订单支付数据
    orderPayLine() {
      let data = {
        startDate: this.orderPayDate[0],
        endDate: this.orderPayDate[1],
      };
      orderPayLine(data).then((res) => {
        this.orderPayLineData = {
          xData: res.data.xData,
          legendData: ["订单总额", "成本总额", "利润总额"],
          firstData: {
            name: "订单总额",
            data: res.data.orderPaySumAmount,
          },
          secondData: {
            name: "利润总额",
            data: res.data.orderPaySumProfit,
          },
          thirdData: {
            name: "成本总额",
            data: res.data.orderPaySumCost,
          },
        };
      });
    },

    //获取广告数据
    getAdvertisement() {
      advertisement().then((res) => {
        this.advertisementLineData = {
          xData: res.data.xData,
          legendData: ["广告预估收益"],
          firstData: {
            name: "广告预估收益",
            data: res.data.yData[0].data,
          },
        };
        this.advertisementData.yestoday = res.data.yestoday;
        this.advertisementData.seven = res.data.seven;
        this.advertisementData.month = res.data.month;
        this.advertisementData.last_month = res.data.last_month;
      });
    },
    //获取订单利润总额
    orderProfit() {
      orderProfitAmount().then((res) => {
        let sum = (
          res.data.payment +
          res.data.send +
          res.data.delivery +
          res.data.complete
        ).toFixed(2);
        this.pieProfitSeriesData = {
          titleData: { text: "利润总额", subtext: sum + "(元)" },
          legendData: ["已支付", "已发货", "已收货", "已完成"],
          name: "利润额",
          data: [
            { value: res.data.payment, name: "已支付" },
            { value: res.data.send, name: "已发货" },
            { value: res.data.delivery, name: "已收货" },
            { value: res.data.complete, name: "已完成" },
          ],
        };
      });
    },
    //获取订单销售总额
    orderSales() {
      orderSalesAmount().then((res) => {
        let sum = (
          res.data.payment +
          res.data.send +
          res.data.delivery +
          res.data.complete
        ).toFixed(2);
        this.pieSalesSeriesData = {
          titleData: { text: "销售总额", subtext: sum + "(元)" },
          legendData: ["已支付", "已发货", "已收货", "已完成"],
          name: "销售额",
          data: [
            { value: res.data.payment, name: "已支付" },
            { value: res.data.send, name: "已发货" },
            { value: res.data.delivery, name: "已收货" },
            { value: res.data.complete, name: "已完成" },
          ],
        };
      });
    },
    //获取支付宝累计金额数据
    getAliPayAmount() {
      aliPayAmount({ month: this.ali.aliMonth }).then((res) => {
        this.ali.aliPaySum = res.data.aliPaySum;
        this.ali.aliPayWithdrawalCommissionSum =
          res.data.aliPayWithdrawalCommissionSum;
        this.ali.aliPayWithdrawalSum = res.data.aliPayWithdrawalSum;
        this.ali.aliPayRefundSum = res.data.aliPayRefundSum;
        this.ali.orderNewcomerSupplyAmountSum =
          res.data.orderNewcomerSupplyAmountSum;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 20px;
  padding-right: 30px;
}
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}
.card-panel-title {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
  text-align: center;
}
.card-panel-text {
  line-height: 38px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 22px;
  margin-bottom: 22px;
  text-align: center;
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
