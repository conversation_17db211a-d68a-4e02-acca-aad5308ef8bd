<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择转账状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入关联订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="payeeAccount">
        <el-input
          v-model="queryParams.payeeAccount"
          placeholder="请输入收款方账户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="payeeRealName">
        <el-input
          v-model="queryParams.payeeRealName"
          placeholder="请输入收款方姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="orderName">
        <el-input
          v-model="queryParams.orderName"
          placeholder="请输入转账备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="date"
          clearable
          @keyup.enter.native="handleQuery"
          value-format="yyyy-MM-dd"
          placeholder="选择申请日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="withdrawalList" border>
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="关联订单号" align="center" prop="orderId" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="收款方账户" align="center" prop="payeeAccount" />
      <el-table-column label="收款方姓名" align="center" prop="payeeRealName" />
      <el-table-column label="金额" align="center" prop="amount" />
      <el-table-column label="转账备注" align="center" prop="orderName" />
      <el-table-column label="是否成功" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="返回信息" align="center" prop="res">
        <template slot-scope="scope">
          {{ scope.row.res ? JSON.parse(scope.row.res).subMsg : "" }}
        </template>
      </el-table-column>
      <el-table-column label="用户主键" align="center" prop="userId" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['payment:withdrawal:reissueRefund']"
            v-if="scope.row.status == 0"
            :loading="loading"
            >重新发起</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listWithdrawal,
  getWithdrawal,
  delWithdrawal,
  addWithdrawal,
  updateWithdrawal,
} from "@/api/payment/withdrawal";

export default {
  name: "Withdrawal",
  dicts: ["sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 支付宝提现信息表格数据
      withdrawalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
        payeeType: null,
        payeeAccount: null,
        payeeRealName: null,
        amount: null,
        orderName: null,
        res: null,
        userId: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询支付宝提现信息列表 */
    getList() {
      this.loading = true;
      listWithdrawal(this.queryParams).then((response) => {
        this.withdrawalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderId: null,
        payeeType: null,
        payeeAccount: null,
        payeeRealName: null,
        amount: null,
        orderName: null,
        res: null,
        userId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      updateWithdrawal(row.id)
        .then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.open = false;
          this.loading = false;
          this.getList();
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>
