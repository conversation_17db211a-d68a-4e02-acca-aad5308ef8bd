<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="">
        <el-date-picker
          v-model="daterangeCreateTime"
          type="daterange"
          range-separator="-"
          value-format="yyyy-MM-dd"
          start-placeholder="开始申请日期"
          end-placeholder="结束申请日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['consume:systemGive:add']"
          >系统赠送</el-button
        >

        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['consume:systemGive:add']"
          >导入</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <div
      style="
        display: flex;
        color: #ffffff;
        text-align: center;
        justify-content: space-around;
        padding: 20px;
      "
    >
      <div style="background-color: #ffa800; min-width: 15%">
        <div style="font-size: 34px">{{ giveSum || 0 }}</div>
        <div style="font-size: 20px">赠送总额</div>
      </div>
    </div>
    <el-table
      v-loading="loading"
      :data="systemGiveList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="赠送数量" align="center" prop="numberCount" />
      <el-table-column label="赠送金额" align="center" prop="consumeAmount" />
      <el-table-column label="赠送时间" align="center" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['consume:systemGive:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改系统赠送富兴卡对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="用户手机号" prop="phonenumber">
          <el-input v-model="form.phonenumber" placeholder="请输入用户手机号" />
        </el-form-item>
        <el-form-item label="赠送富星卡额" prop="consumeAmount">
          <el-input-number
            v-model="form.consumeAmount"
            :min="1"
            :max="1000"
            :precision="0"
            disabled
            placeholder="请输入富星卡额"
          />
        </el-form-item>
        <el-form-item label="赠送总数量" prop="numberCount">
          <el-input-number
            v-model="form.numberCount"
            :min="1"
            :max="10000"
            :precision="0"
            placeholder="赠送总数量"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSystemGive,
  getSystemGive,
  delSystemGive,
  addSystemGive,
  listSystemGiveSum,
} from "@/api/consume/systemGive";
import { getToken } from "@/utils/auth";
export default {
  name: "SystemGive",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统赠送富兴卡表格数据
      systemGiveList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //申请时间查询范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        numberCount: null,
        consumeAmount: null,
        userId: null,
        params: {
          phonenumber: null,
          endCreateTime: null,
          beginCreateTime: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        phonenumber: [
          { required: true, message: "请输入用户手机号", trigger: "blur" },
        ],
        consumeAmount: [
          { required: true, message: "请输入富星卡额", trigger: "blur" },
        ],
        numberCount: [
          { required: true, message: "请输入赠送总数量", trigger: "blur" },
        ],
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,

        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/consume/systemGive/importData",
      },
      //系统赠送总额
      giveSum: 0,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询系统赠送富兴卡列表 */
    getList() {
      this.loading = true;
      if (null != this.daterangeCreateTime && "" != this.daterangeCreateTime) {
        this.queryParams.params.beginCreateTime = this.daterangeCreateTime[0];
        this.queryParams.params.endCreateTime = this.daterangeCreateTime[1];
      } else {
        this.queryParams.params.beginCreateTime = null;
        this.queryParams.params.endCreateTime = null;
      }
      listSystemGive(this.queryParams).then((response) => {
        this.systemGiveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      listSystemGiveSum(this.queryParams).then((response) => {
        this.giveSum = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        numberCount: null,
        consumeAmount: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        phonenumber: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "系统赠送富兴卡";
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          addSystemGive(this.form).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "/consume/systemGive/export",
        {},
        `系统赠送富星卡模板_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除系统赠送富兴卡编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSystemGive(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
