<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >

        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['consume:szqz:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="szqzList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="富星卡金额" align="center" prop="consumeAmount" />
      <el-table-column label="兑换数量" align="center" prop="exchangeNumber" />
      <el-table-column label="兑换数字权证" align="center" prop="szqz" />
      <el-table-column label="兑换比例" align="center" prop="proportion" />
      <el-table-column
        label="兑换所需通用积分"
        align="center"
        prop="integralTy"
      />
      <el-table-column label="兑换所需集团股" align="center" prop="share" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改兑换权证对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>

        <el-form-item label="消费卡金额" prop="consumeAmount">
          <el-input
            v-model="form.consumeAmount"
            placeholder="请输入消费卡金额"
          />
        </el-form-item>

        <el-form-item label="兑换数字权证" prop="szqz">
          <el-input v-model="form.szqz" placeholder="请输入兑换数字权证" />
        </el-form-item>
        <el-form-item label="兑换比例" prop="proportion">
          <el-input v-model="form.proportion" placeholder="请输入兑换比例" />
        </el-form-item>
        <el-form-item label="兑换所需通用积分" prop="integralTy">
          <el-input
            v-model="form.integralTy"
            placeholder="请输入兑换所需通用积分"
          />
        </el-form-item>
        <el-form-item label="兑换所需集团股" prop="share">
          <el-input v-model="form.share" placeholder="请输入兑换所需集团股" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSzqz,
  getSzqz,
  delSzqz,
  addSzqz,
  updateSzqz,
} from "@/api/consume/szqz";

export default {
  name: "Szqz",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 兑换权证表格数据
      szqzList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        szqz: null,
        proportion: null,
        integralTy: null,
        share: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户主键不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询兑换权证列表 */
    getList() {
      this.loading = true;
      listSzqz(this.queryParams).then((response) => {
        this.szqzList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        szqz: null,
        proportion: null,
        integralTy: null,
        share: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加兑换权证";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSzqz(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改兑换权证";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSzqz(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSzqz(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除兑换权证编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSzqz(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consume/szqz/export",
        {
          ...this.queryParams,
        },
        `szqz_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
