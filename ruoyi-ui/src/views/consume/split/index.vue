<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="富星卡卡号" prop="consumeNumber">
        <el-input
          v-model="queryParams.consumeNumber"
          placeholder="请输入富星卡卡号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拆分类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择拆分类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_consume_split_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="splitList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUser.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column
        label="富星卡卡号"
        align="center"
        prop="consumeNumber"
        min-width="120"
      />
      <el-table-column label="拆分类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_consume_split_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column label="消耗百分比" align="center" prop="consume" />
      <el-table-column label="操作时间" align="center" prop="createTime" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleInfo(scope.row)"
            v-if="scope.row.type == 1"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改富星卡拆分对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-table v-loading="loading" :data="consumeList" border>
        <el-table-column label="富星卡名称" align="center" prop="consumeName" />
        <el-table-column
          label="富星卡金额"
          align="center"
          prop="consumeAmount"
        />
        <el-table-column
          label="富星卡卡号"
          align="center"
          prop="consumeNumber"
          min-width="110px"
        />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.sxsc_consume_status"
              :value="scope.row.status"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSplit,
  getSplit,
  delSplit,
  addSplit,
  updateSplit,
} from "@/api/consume/split";

export default {
  name: "Split",
  dicts: ["sxsc_consume_split_type", "sxsc_consume_status"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 富星卡拆分表格数据
      splitList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      consumeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeNumber: null,
        type: null,
        splitConsumeNumber: null,
        splitConsumeAmount: null,
        splitIntegralSj: null,
        consume: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        consumeNumber: [
          { required: true, message: "富星卡卡号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询富星卡拆分列表 */
    getList() {
      this.loading = true;
      listSplit(this.queryParams).then((response) => {
        this.splitList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeNumber: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        type: null,
        splitConsumeNumber: null,
        splitConsumeAmount: null,
        splitIntegralSj: null,
        consume: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加富星卡拆分";
    },
    handleInfo(row) {
      const id = row.id || this.ids;
      getSplit(id).then((response) => {
        this.form = response.data;
        this.consumeList = response.data.consumeList;
        this.open = true;
        this.title = "富星卡拆分详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSplit(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改富星卡拆分";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSplit(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSplit(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除富星卡拆分编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSplit(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "person/split/export",
        {
          ...this.queryParams,
        },
        `split_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
