<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户账号" prop="queryParams.params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入用户账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="额度类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择额度类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sxsc_consume_quota_detail_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['consumeQuota:detail:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          plain
          size="mini"
          @click="openBatch = true"
          v-hasPermi="['consumeQuota:detail:add']"
          >批量操作</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['consumeQuota:detail:export']"
          >导出</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="detailList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column
        label="手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column label="额度" align="center" prop="quota" />
      <el-table-column label="额度类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_consume_quota_detail_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改额度明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="用户手机号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户手机号" />
        </el-form-item>
        <el-form-item label="额度类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择额度类型">
            <el-option
              v-for="dict in dict.type.sxsc_consume_quota_detail_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="额度" prop="quota">
          <el-input-number v-model="form.quota" placeholder="请输入额度" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <div>
      <el-dialog
        :visible.sync="openBatch"
        width="500px"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <el-radio-group v-model="batchQuotaType">
          <el-radio
            v-for="dict in dict.type.sxsc_consume_quota_detail_type"
            :key="dict.value"
            :label="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
        <el-input
          style="margin-top: 20px"
          v-model="batchText"
          type="textarea"
          :rows="5"
          placeholder="批量操作积分，文本格式如下：17600009999(手机号),100(额度);"
        ></el-input>

        <div slot="footer" class="dialog-footer">
          <el-button :loading="batchLoading" type="primary" @click="batchClick"
            >确定</el-button
          >
          <el-button @click="openBatch = false">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listDetail,
  getDetail,
  delDetail,
  addDetail,
  updateDetail,
  htRechargeBatch,
} from "@/api/consume/consumeQuota";

export default {
  name: "Detail",
  dicts: ["sxsc_consume_quota_detail_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      batchLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 额度明细表格数据
      detailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openBatch: false,
      batchQuotaType: null,
      batchText: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        quota: null,
        type: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "用户不能为空", trigger: "blur" },
        ],
        quota: [
          {
            required: true,
            message: "富星卡获取额度不能为空",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "额度类型不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询额度明细列表 */
    getList() {
      this.loading = true;
      listDetail(this.queryParams).then((response) => {
        this.detailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        userName: null,
        quota: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        type: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加额度明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDetail(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改额度明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDetail(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDetail(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除额度明细编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDetail(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consumeQuota/detail/export",
        {
          ...this.queryParams,
        },
        `detail_${new Date().getTime()}.xlsx`
      );
    },
    //批量积分操作
    batchClick() {
      this.batchLoading = true;
      htRechargeBatch(
        { text: this.batchText.replace(/\n/g, "") },
        this.batchQuotaType
      )
        .then((res) => {
          this.$alert(
            "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
              res.msg +
              "</div>",
            "导入结果",
            { dangerouslyUseHTMLString: true }
          );
          this.getList();
          this.openBatch = false;
          this.batchLoading = false;
        })
        .catch((res) => {
          this.batchLoading = false;
        });
    },
  },
};
</script>
