<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="用户手机号" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="富兴卡名称" prop="consumeName">
        <el-input
          v-model="queryParams.consumeName"
          placeholder="请输入富兴卡名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="富兴卡卡号" prop="consumeNumber">
        <el-input
          v-model="queryParams.consumeNumber"
          placeholder="请输入富兴卡卡号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['consume:enterpriseShare:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="enterpriseShareList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="唯一标识" align="center" prop="id" />
      <el-table-column
        label="用户昵称"
        align="center"
        prop="sysUserMain.nickName"
      />
      <el-table-column
        label="用户手机号"
        align="center"
        prop="sysUserMain.phonenumber"
      />
      <el-table-column label="富兴卡名称" align="center" prop="consumeName" />
      <el-table-column label="富兴卡金额" align="center" prop="consumeAmount" />
      <el-table-column label="富兴卡卡号" align="center" prop="consumeNumber" />
      <el-table-column label="兑换企业股" align="center" prop="share" />
      <el-table-column label="兑换比例" align="center" prop="proportion" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改兑换企业股对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户主键" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户主键" />
        </el-form-item>
        <el-form-item label="富兴卡名称" prop="consumeName">
          <el-input v-model="form.consumeName" placeholder="请输入富兴卡名称" />
        </el-form-item>
        <el-form-item label="富兴卡金额" prop="consumeAmount">
          <el-input
            v-model="form.consumeAmount"
            placeholder="请输入富兴卡金额"
          />
        </el-form-item>
        <el-form-item label="富兴卡卡号" prop="consumeNumber">
          <el-input
            v-model="form.consumeNumber"
            placeholder="请输入富兴卡卡号"
          />
        </el-form-item>
        <el-form-item label="兑换集团股份" prop="share">
          <el-input v-model="form.share" placeholder="请输入兑换集团股份" />
        </el-form-item>
        <el-form-item label="兑换比例" prop="proportion">
          <el-input v-model="form.proportion" placeholder="请输入兑换比例" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEnterpriseShare,
  getEnterpriseShare,
  delEnterpriseShare,
  addEnterpriseShare,
  updateEnterpriseShare,
} from "@/api/consume/enterpriseShare";

export default {
  name: "EnterpriseShare",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 兑换企业股表格数据
      enterpriseShareList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        consumeNumber: null,
        share: null,
        proportion: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户主键不能为空", trigger: "blur" },
        ],
        consumeNumber: [
          { required: true, message: "富兴卡卡号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询兑换企业股列表 */
    getList() {
      this.loading = true;
      listEnterpriseShare(this.queryParams).then((response) => {
        this.enterpriseShareList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        consumeName: null,
        consumeAmount: null,
        consumeNumber: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        share: null,
        proportion: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加兑换企业股";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEnterpriseShare(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改兑换企业股";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEnterpriseShare(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEnterpriseShare(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除兑换企业股编号为"' + ids + '"的数据项？')
        .then(function () {
          return delEnterpriseShare(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "consume/enterpriseShare/export",
        {
          ...this.queryParams,
        },
        `enterpriseShare_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
