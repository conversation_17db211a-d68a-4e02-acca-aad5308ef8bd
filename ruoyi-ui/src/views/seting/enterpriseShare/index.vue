<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="分红时间" prop="bonusTime">
        <el-date-picker
          clearable
          v-model="queryParams.bonusTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择分红时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <!--<el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['seting:enterpriseShare:export']"
        >导出</el-button>-->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="enterpriseShareList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column
        label="当前企业总股"
        align="center"
        prop="enterpriseShare"
      />
      <el-table-column
        label="当前企业股权单价"
        align="center"
        prop="shareUnit"
      />
      <el-table-column label="分红时间" align="center" prop="bonusTime" />
      <el-table-column label="分红总金额" align="center" prop="bonusAmount" />
      <el-table-column label="分红状态" align="center" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? "未分红" : "已分红" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="scope.row.status == 0"
            v-hasPermi="['seting:enterpriseShare:edit']"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业股分红设置信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="企业股权单价" prop="shareUnit">
          <el-input-number
            :precision="2"
            :step="0.1"
            :min="0.01"
            v-model="form.shareUnit"
            placeholder="请输入当前企业股权单价"
          />
        </el-form-item>
        <el-form-item label="分红时间" prop="bonusTime">
          <el-date-picker
            clearable
            v-model="form.bonusTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择分红时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="分红明细"
      :visible.sync="openDetail"
      width="60%"
      append-to-body
    >
      <el-form label-width="100px" :inline="true">
        <el-form-item label="">
          <el-input
            v-model="detail.params.phonenumber"
            placeholder="请输入用户手机号"
            clearable
          />
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getDetailList"
          >搜索</el-button
        ></el-form
      >
      <el-table v-loading="loading" :data="detailList" border>
        <el-table-column label="用户主键" align="center" prop="userId" />
        <el-table-column
          label="用户昵称"
          align="center"
          prop="sysUser.nickName"
        />
        <el-table-column
          label="用户手机号"
          align="center"
          prop="sysUser.phonenumber"
        />
        <el-table-column
          label="本次分红用户总企业股"
          align="center"
          prop="share"
        />
        <el-table-column
          label="本次分红总佣金"
          align="center"
          prop="commission"
        />
      </el-table>
      <pagination
        v-show="detail.total > 0"
        :total="detail.total"
        :page.sync="detail.pageNum"
        :limit.sync="detail.pageSize"
        @pagination="getDetailList"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  listEnterpriseShare,
  getEnterpriseShare,
  addEnterpriseShare,
  updateEnterpriseShare,
  listDetail,
} from "@/api/seting/enterpriseShare";

export default {
  name: "EnterpriseShare",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业股分红设置信息表格数据
      enterpriseShareList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 明细弹出层
      openDetail: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        enterpriseShare: null,
        shareUnit: null,
        bonusTime: null,
        bonusAmount: null,
        status: null,
      },
      detail: {
        enterpriseShareId: 0,
        total: 0,
        pageNum: 1,
        pageSize: 10,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        shareUnit: [
          {
            required: true,
            message: "当前企业股权单价不能为空",
            trigger: "blur",
          },
        ],
        bonusTime: [
          { required: true, message: "分红时间不能为空", trigger: "blur" },
        ],
        bonusAmount: [
          { required: true, message: "分红总金额不能为空", trigger: "blur" },
        ],
      },
      detailList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业股分红设置信息列表 */
    getList() {
      this.loading = true;
      listEnterpriseShare(this.queryParams).then((response) => {
        this.enterpriseShareList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getDetailList() {
      listDetail(this.detail).then((response) => {
        this.detailList = response.rows;
        this.detail.total = response.total;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        enterpriseShare: null,
        shareUnit: null,
        bonusTime: null,
        bonusAmount: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 明细按钮操作 */
    handleDetail(row) {
      this.openDetail = true;
      this.detail.enterpriseShareId = row.id;
      this.getDetailList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEnterpriseShare(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业股分红设置信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEnterpriseShare(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEnterpriseShare(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "seting/enterpriseShare/export",
        {
          ...this.queryParams,
        },
        `enterpriseShare_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
