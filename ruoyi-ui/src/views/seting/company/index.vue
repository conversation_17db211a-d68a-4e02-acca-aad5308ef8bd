<template>
  <div class="app-container">
    <el-form
      v-if="isShowOpt"
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="公司名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否上市" prop="listed">
        <el-select
          v-model="queryParams.listed"
          placeholder="请选择是否上市"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['seting:company:add']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" v-if="isShowOpt">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="companyList" border>
      <el-table-column label="公司名称" align="center" prop="name" />
      <el-table-column label="公司头像地址" align="center" prop="imgUrl">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="是否上市" align="center" prop="listed">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.listed" />
        </template>
      </el-table-column>
      <el-table-column label="人数" align="center" prop="personNumber" />
      <el-table-column label="公司标签" align="center" prop="label">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sxsc_joint_company_label"
            :value="scope.row.label ? scope.row.label.split(',') : []"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleInfo(scope.row)"
            v-if="!isShowOpt"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-if="isShowOpt"
            v-hasPermi="['seting:company:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-if="isShowOpt"
            v-hasPermi="['seting:company:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改合资公司对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="50%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="公司名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="公司头像" prop="imgUrl">
          <image-upload v-model="form.imgUrl" :limit="1" :isShowTip="false" />
        </el-form-item>
        <el-form-item label="是否上市" prop="listed">
          <el-radio-group v-model="form.listed">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="公司人数" prop="personNumber">
          <el-input-number
            v-model="form.personNumber"
            placeholder="请输入人数"
          />
        </el-form-item>
        <el-form-item label="公司标签" prop="label">
          <el-checkbox-group v-model="form.label">
            <el-checkbox
              v-for="dict in dict.type.sxsc_joint_company_label"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="合同信息" prop="contractFile">
          <file-upload
            v-model="form.contractFile"
            :isShowTip="false"
            :fileType="fileType"
          />
        </el-form-item>
        <el-divider content-position="center"
          >合资公司股东明细数据信息</el-divider
        >
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAddSxscSetingJointCompanyDetail"
              >添加</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleDeleteSxscSetingJointCompanyDetail"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <el-table
          :data="details"
          :row-class-name="rowSxscSetingJointCompanyDetailIndex"
          @selection-change="handleSxscSetingJointCompanyDetailSelectionChange"
          ref="sxscSetingJointCompanyDetail"
          border
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="股东名称" prop="shareholder" min-width="150">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.shareholder"
                placeholder="请输入股东名称"
              />
            </template>
          </el-table-column>
          <el-table-column label="持股比例" prop="proportion" min-width="160">
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                :step="0.1"
                :max="100"
                v-model="scope.row.proportion"
                placeholder="请输入持股比例"
              />
            </template>
          </el-table-column>
          <el-table-column label="出资方式" prop="mode" min-width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.mode" placeholder="请选择出资方式">
                <el-option
                  v-for="dict in dict.type.sxsc_joint_company_mode"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="市场价值" prop="marketValue" min-width="160">
            <template slot-scope="scope">
              <el-input-number
                :precision="2"
                :step="0.1"
                :min="0"
                v-model="scope.row.marketValue"
                placeholder="请输入市场价值"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="title"
      :visible.sync="openInfo"
      width="50%"
      append-to-body
    >
      <el-form :model="form" label-width="100px">
        <el-divider content-position="center">合同信息</el-divider>
        <file-upload
          v-model="form.contractFile"
          :isShowTip="false"
          :fileType="fileType"
          :operation="false"
        />
        <div style="margin-top: 50px">
          <el-divider content-position="center"
            >合资公司股东明细数据信息</el-divider
          >
        </div>
        <el-table :data="details" border style="margin-top: 50px">
          <el-table-column
            label="股东名称"
            prop="shareholder"
            min-width="150"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="持股比例"
            prop="proportion"
            min-width="160"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="出资方式"
            prop="mode"
            min-width="150"
            align="center"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sxsc_joint_company_mode"
                :value="scope.row.mode"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="市场价值"
            prop="marketValue"
            min-width="160"
            align="center"
          >
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCompany,
  getCompany,
  delCompany,
  addCompany,
  updateCompany,
} from "@/api/seting/company";

export default {
  name: "Company",
  dicts: ["sxsc_joint_company_mode", "sys_yes_no", "sxsc_joint_company_label"],
  props: {
    // 是否显示操作
    isShowOpt: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //上传附件类型
      fileType: ["png", "jpg", "jpeg", "pdf"],
      // 子表选中数据
      checkedSxscSetingJointCompanyDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 合资公司表格数据
      companyList: [],
      // 合资公司股东明细数据表格数据
      details: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openInfo: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        listed: null,
        label: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "公司名称不能为空", trigger: "blur" },
        ],
        listed: [
          { required: true, message: "是否上市不能为空", trigger: "change" },
        ],
        personNumber: [
          { required: true, message: "人数不能为空", trigger: "blur" },
        ],
        label: [
          { required: true, message: "公司标签不能为空", trigger: "blur" },
        ],
        contractFile: [
          { required: true, message: "合同地址不能为空", trigger: "blur" },
        ],
        imgUrl: [
          { required: true, message: "公司头像地址不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询合资公司列表 */
    getList() {
      this.loading = true;
      listCompany(this.queryParams).then((response) => {
        this.companyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        imgUrl: null,
        listed: null,
        personNumber: null,
        label: [],
        contractFile: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.details = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加合资公司";
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      const id = row.id || this.ids;
      getCompany(id).then((response) => {
        this.form = response.data;
        this.form.label = this.form.label.split(",");
        this.details = response.data.details;
        this.openInfo = true;
        this.title = "合资公司";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getCompany(id).then((response) => {
        this.form = response.data;
        this.form.label = this.form.label.split(",");
        this.details = response.data.details;
        this.open = true;
        this.title = "修改合资公司";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let data = { ...this.form };
          data.label = this.form.label.join(",");
          data.details = this.details;
          if (this.form.id != null) {
            updateCompany(data).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCompany(data).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        strs += list[i].url + separator;
      }
      return strs != "" ? strs.substr(0, strs.length - 1) : "";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除合资公司？")
        .then(function () {
          return delCompany(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 合资公司股东明细数据序号 */
    rowSxscSetingJointCompanyDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 合资公司股东明细数据添加按钮操作 */
    handleAddSxscSetingJointCompanyDetail() {
      let obj = {};
      obj.shareholder = "";
      obj.proportion = "";
      obj.mode = "";
      obj.marketValue = "";
      this.details.push(obj);
    },
    /** 合资公司股东明细数据删除按钮操作 */
    handleDeleteSxscSetingJointCompanyDetail() {
      if (this.checkedSxscSetingJointCompanyDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的合资公司股东明细数据数据");
      } else {
        const details = this.details;
        const checkedSxscSetingJointCompanyDetail =
          this.checkedSxscSetingJointCompanyDetail;
        this.details = details.filter(function (item) {
          return checkedSxscSetingJointCompanyDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleSxscSetingJointCompanyDetailSelectionChange(selection) {
      this.checkedSxscSetingJointCompanyDetail = selection.map(
        (item) => item.index
      );
    },
  },
};
</script>
