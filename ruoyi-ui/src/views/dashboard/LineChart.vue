<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "350px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.setOptions(this.chartData);
    },
    setOptions({
      xData = [],
      legendData = [],
      firstData = { name: "", data: [] },
      secondData = { name: "", data: [] },
      thirdData = { name: "", data: [] },
      fourthData = { name: "", data: [] },
      fifthData = { name: "", data: [] },
    } = {}) {
      this.chart.setOption({
        xAxis: {
          data: xData,
          boundaryGap: false,
          axisTick: {
            show: false,
          },
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          padding: [5, 10],
        },
        yAxis: {
          axisTick: {
            show: false,
          },
        },
        legend: {
          data: legendData,
        },

        series: [
          {
            name: firstData.name,
            smooth: true,
            type: "line",
            data: firstData.data,
            animationDuration: 2800,
            animationEasing: "cubicInOut",
          },
          {
            name: secondData.name,
            smooth: true,
            type: "line",
            data: secondData.data,
            animationDuration: 2800,
            animationEasing: "quadraticOut",
          },
          {
            name: thirdData.name,
            smooth: true,
            type: "line",
            data: thirdData.data,
            animationDuration: 2800,
            animationEasing: "linear",
          },
          {
            name: fourthData.name,
            smooth: true,
            type: "line",
            data: fourthData.data,
            animationDuration: 2800,
            animationEasing: "linear",
          },
          {
            name: fifthData.name,
            smooth: true,
            type: "line",
            data: fifthData.data,
            animationDuration: 2800,
            animationEasing: "linear",
          },
        ],
      });
    },
  },
};
</script>
