<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="团长账号" prop="params.phonenumber">
        <el-input
          v-model="queryParams.params.phonenumber"
          placeholder="请输入团长账号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="达标获得积分" prop="integrate">
        <el-input
          v-model="queryParams.integrate"
          placeholder="请输入团长达标获得积分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="达标人数" prop="reachPerson">
        <el-input
          v-model="queryParams.reachPerson"
          placeholder="请输入达标人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
        <!-- <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bill:groupBuyingReward:add']"
          >新增</el-button
        > -->
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="groupBuyingRewardList"
      @selection-change="handleSelectionChange"
      border
    >
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="昵称" align="center" prop="sysUser.nickName" />
      <el-table-column
        label="手机号"
        align="center"
        prop="sysUser.phonenumber"
      />
      <el-table-column
        label="团长达标获得积分"
        align="center"
        prop="integrate"
      />
      <el-table-column label="达标人数" align="center" prop="reachPerson" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bill:groupBuyingReward:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bill:groupBuyingReward:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改团长达标奖励积分对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="团长主键" prop="userId">
          <el-input
            v-model="form.userId"
            placeholder="请输入团长主键"
            disabled
          />
        </el-form-item>
        <el-form-item label="团长达标获得积分" prop="integrate">
          <el-input-number
            v-model="form.integrate"
            placeholder="请输入团长达标获得积分"
          />
        </el-form-item>
        <el-form-item label="达标人数" prop="reachPerson">
          <el-input-number
            v-model="form.reachPerson"
            placeholder="请输入达标人数"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listGroupBuyingReward,
  getGroupBuyingReward,
  delGroupBuyingReward,
  addGroupBuyingReward,
  updateGroupBuyingReward,
} from "@/api/bill/groupBuyingReward";

export default {
  name: "GroupBuyingReward",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 团长达标奖励积分表格数据
      groupBuyingRewardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        integrate: null,
        reachPerson: null,
        params: {
          phonenumber: null,
        },
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询团长达标奖励积分列表 */
    getList() {
      this.loading = true;
      listGroupBuyingReward(this.queryParams).then((response) => {
        this.groupBuyingRewardList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        integrate: null,
        reachPerson: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加团长达标奖励积分";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getGroupBuyingReward(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改团长达标奖励积分";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateGroupBuyingReward(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGroupBuyingReward(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除团长达标奖励积分编号为"' + ids + '"的数据项？')
        .then(function () {
          return delGroupBuyingReward(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "bill/groupBuyingReward/export",
        {
          ...this.queryParams,
        },
        `groupBuyingReward_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
