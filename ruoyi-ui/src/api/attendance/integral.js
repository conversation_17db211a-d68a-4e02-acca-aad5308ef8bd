import request from '@/utils/request'

// 查询签到积分列表
export function listIntegral(query) {
  return request({
    url: '/attendance/integral/list',
    method: 'get',
    params: query
  })
}

// 查询签到积分详细
export function getIntegral(id) {
  return request({
    url: '/attendance/integral/' + id,
    method: 'get'
  })
}

// 新增签到积分
export function addIntegral(data) {
  return request({
    url: '/attendance/integral',
    method: 'post',
    data: data
  })
}

// 修改签到积分
export function updateIntegral(data) {
  return request({
    url: '/attendance/integral',
    method: 'put',
    data: data
  })
}

// 删除签到积分
export function delIntegral(id) {
  return request({
    url: '/attendance/integral/' + id,
    method: 'delete'
  })
}
