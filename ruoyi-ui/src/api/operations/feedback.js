import request from '@/utils/request'

// 查询投诉反馈列表
export function listFeedback(query) {
  return request({
    url: '/operations/feedback/list',
    method: 'get',
    params: query
  })
}

// 查询投诉反馈详细
export function getFeedback(id) {
  return request({
    url: '/operations/feedback/' + id,
    method: 'get'
  })
}

// 新增投诉反馈
export function addFeedback(data) {
  return request({
    url: '/operations/feedback',
    method: 'post',
    data: data
  })
}

// 修改投诉反馈
export function updateFeedback(data) {
  return request({
    url: '/operations/feedback',
    method: 'put',
    data: data
  })
}

// 删除投诉反馈
export function delFeedback(id) {
  return request({
    url: '/operations/feedback/' + id,
    method: 'delete'
  })
}
