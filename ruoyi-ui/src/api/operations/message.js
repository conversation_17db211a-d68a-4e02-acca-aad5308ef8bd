import request from '@/utils/request'

// 查询首页消息区列表
export function listMessage(query) {
  return request({
    url: '/operations/message/list',
    method: 'get',
    params: query
  })
}

// 查询首页消息区详细
export function getMessage(id) {
  return request({
    url: '/operations/message/' + id,
    method: 'get'
  })
}

// 新增首页消息区
export function addMessage(data) {
  return request({
    url: '/operations/message',
    method: 'post',
    data: data
  })
}

// 修改首页消息区
export function updateMessage(data) {
  return request({
    url: '/operations/message',
    method: 'put',
    data: data
  })
}

// 删除首页消息区
export function delMessage(id) {
  return request({
    url: '/operations/message/' + id,
    method: 'delete'
  })
}
