import request from '@/utils/request'

// 查询隐私协议列表
export function listAgreement(query) {
  return request({
    url: '/operations/agreement/list',
    method: 'get',
    params: query
  })
}

// 查询隐私协议详细
export function getAgreement(id) {
  return request({
    url: '/operations/agreement/' + id,
    method: 'get'
  })
}

// 新增隐私协议
export function addAgreement(data) {
  return request({
    url: '/operations/agreement',
    method: 'post',
    data: data
  })
}

// 修改隐私协议
export function updateAgreement(data) {
  return request({
    url: '/operations/agreement',
    method: 'put',
    data: data
  })
}

// 删除隐私协议
export function delAgreement(id) {
  return request({
    url: '/operations/agreement/' + id,
    method: 'delete'
  })
}
