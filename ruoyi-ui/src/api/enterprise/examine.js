import request from '@/utils/request'

// 查询企业审核记录列表
export function listExamine(query) {
  return request({
    url: '/enterprise/examine/list',
    method: 'get',
    params: query
  })
}

// 查询企业审核记录详细
export function getExamine(id) {
  return request({
    url: '/enterprise/examine/' + id,
    method: 'get'
  })
}

// 新增企业审核记录
export function addExamine(data) {
  return request({
    url: '/enterprise/examine',
    method: 'post',
    data: data
  })
}

// 修改企业审核记录
export function updateExamine(data) {
  return request({
    url: '/enterprise/examine',
    method: 'put',
    data: data
  })
}

// 删除企业审核记录
export function delExamine(id) {
  return request({
    url: '/enterprise/examine/' + id,
    method: 'delete'
  })
}
