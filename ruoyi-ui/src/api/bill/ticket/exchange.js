import request from "@/utils/request";

// 查询数字权证兑换票证列表
export function listExchange(query) {
  return request({
    url: "/bill/ticket/exchange/list",
    method: "get",
    params: query,
  });
}
// 查询数字权证兑换票证列表
export function listExchangeSum(query) {
  return request({
    url: "/bill/ticket/exchange/listSum",
    method: "get",
    params: query,
  });
}
// 查询数字权证兑换票证详细
export function getExchange(id) {
  return request({
    url: "/bill/ticket/exchange/" + id,
    method: "get",
  });
}

// 新增数字权证兑换票证
export function addExchange(data) {
  return request({
    url: "/bill/ticket/exchange",
    method: "post",
    data: data,
  });
}

// 修改数字权证兑换票证
export function updateExchange(data) {
  return request({
    url: "/bill/ticket/exchange",
    method: "put",
    data: data,
  });
}

// 删除数字权证兑换票证
export function delExchange(id) {
  return request({
    url: "/bill/ticket/exchange/" + id,
    method: "delete",
  });
}
