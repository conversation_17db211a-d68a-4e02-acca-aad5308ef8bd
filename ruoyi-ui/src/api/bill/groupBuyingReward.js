import request from '@/utils/request'

// 查询团长达标奖励积分列表
export function listGroupBuyingReward(query) {
  return request({
    url: '/bill/groupBuyingReward/list',
    method: 'get',
    params: query
  })
}

// 查询团长达标奖励积分详细
export function getGroupBuyingReward(id) {
  return request({
    url: '/bill/groupBuyingReward/' + id,
    method: 'get'
  })
}

// 新增团长达标奖励积分
export function addGroupBuyingReward(data) {
  return request({
    url: '/bill/groupBuyingReward',
    method: 'post',
    data: data
  })
}

// 修改团长达标奖励积分
export function updateGroupBuyingReward(data) {
  return request({
    url: '/bill/groupBuyingReward',
    method: 'put',
    data: data
  })
}

// 删除团长达标奖励积分
export function delGroupBuyingReward(id) {
  return request({
    url: '/bill/groupBuyingReward/' + id,
    method: 'delete'
  })
}
