import request from "@/utils/request";

// 查询股权分红列表
export function listShareBonus(query) {
  return request({
    url: "/person/shareBonus/list",
    method: "get",
    params: query,
  });
}

// 查询股权分红明细列表
export function listBonusDetails(query) {
  return request({
    url: "/person/shareBonus/details",
    method: "get",
    params: query,
  });
}
// 查询股权分红详细
export function getShareBonus(id) {
  return request({
    url: "/person/shareBonus/" + id,
    method: "get",
  });
}

// 新增股权分红
export function addShareBonus(data) {
  return request({
    url: "/person/shareBonus",
    method: "post",
    data: data,
  });
}

// 修改股权分红
export function updateShareBonus(data) {
  return request({
    url: "/person/shareBonus",
    method: "put",
    data: data,
  });
}

// 删除股权分红
export function delShareBonus(id) {
  return request({
    url: "/person/shareBonus/" + id,
    method: "delete",
  });
}
// 查询股权分红明细列表
export function listBonusDetailsConsume(query) {
  return request({
    url: "/person/shareBonus/consume/details",
    method: "get",
    params: query,
  });
}
