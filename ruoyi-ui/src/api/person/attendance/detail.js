import request from '@/utils/request'

// 查询人员签到明细列表
export function listDetail(query) {
  return request({
    url: '/attendance/detail/list',
    method: 'get',
    params: query
  })
}

// 查询人员签到明细详细
export function getDetail(id) {
  return request({
    url: '/attendance/detail/' + id,
    method: 'get'
  })
}

// 新增人员签到明细
export function addDetail(data) {
  return request({
    url: '/attendance/detail',
    method: 'post',
    data: data
  })
}

// 修改人员签到明细
export function updateDetail(data) {
  return request({
    url: '/attendance/detail',
    method: 'put',
    data: data
  })
}

// 删除人员签到明细
export function delDetail(id) {
  return request({
    url: '/attendance/detail/' + id,
    method: 'delete'
  })
}
