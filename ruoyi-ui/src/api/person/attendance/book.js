import request from '@/utils/request'

// 查询人员签到信息列表
export function listBook(query) {
  return request({
    url: '/attendance/book/list',
    method: 'get',
    params: query
  })
}

// 查询人员签到信息详细
export function getBook(id) {
  return request({
    url: '/attendance/book/' + id,
    method: 'get'
  })
}

// 新增人员签到信息
export function addBook(data) {
  return request({
    url: '/attendance/book',
    method: 'post',
    data: data
  })
}

// 修改人员签到信息
export function updateBook(data) {
  return request({
    url: '/attendance/book',
    method: 'put',
    data: data
  })
}

// 删除人员签到信息
export function delBook(id) {
  return request({
    url: '/attendance/book/' + id,
    method: 'delete'
  })
}
