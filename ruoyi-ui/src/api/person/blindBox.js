import request from "@/utils/request";

// 查询盲盒信息列表
export function listBlindBox(query) {
  return request({
    url: "/person/blindBox/list",
    method: "get",
    params: query,
  });
}

// 查询盲盒信息详细
export function getBlindBox(id) {
  return request({
    url: "/person/blindBox/" + id,
    method: "get",
  });
}

// 新增盲盒信息
export function addBlindBox(data) {
  return request({
    url: "/person/blindBox",
    method: "post",
    data: data,
  });
}
