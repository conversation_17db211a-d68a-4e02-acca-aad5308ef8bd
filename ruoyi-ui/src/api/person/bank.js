import request from '@/utils/request'

// 查询银行卡信息列表
export function listBank(query) {
  return request({
    url: '/person/bank/list',
    method: 'get',
    params: query
  })
}

// 查询银行卡信息详细
export function getBank(id) {
  return request({
    url: '/person/bank/' + id,
    method: 'get'
  })
}

// 新增银行卡信息
export function addBank(data) {
  return request({
    url: '/person/bank',
    method: 'post',
    data: data
  })
}

// 修改银行卡信息
export function updateBank(data) {
  return request({
    url: '/person/bank',
    method: 'put',
    data: data
  })
}

// 删除银行卡信息
export function delBank(id) {
  return request({
    url: '/person/bank/' + id,
    method: 'delete'
  })
}
