import request from '@/utils/request'

// 查询赠送积分列表
export function listIntegralGive(query) {
  return request({
    url: '/person/integralGive/list',
    method: 'get',
    params: query
  })
}

// 查询赠送积分详细
export function getIntegralGive(userId) {
  return request({
    url: '/person/integralGive/' + userId,
    method: 'get'
  })
}

// 新增赠送积分
export function addIntegralGive(data) {
  return request({
    url: '/person/integralGive',
    method: 'post',
    data: data
  })
}

// 修改赠送积分
export function updateIntegralGive(data) {
  return request({
    url: '/person/integralGive',
    method: 'put',
    data: data
  })
}

// 删除赠送积分
export function delIntegralGive(userId) {
  return request({
    url: '/person/integralGive/' + userId,
    method: 'delete'
  })
}
