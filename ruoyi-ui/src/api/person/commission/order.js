import request from '@/utils/request'

// 查询佣金提现列表
export function listOrder(query) {
  return request({
    url: '/commission/order/list',
    method: 'get',
    params: query
  })
}

// 查询佣金提现详细
export function getOrder(id) {
  return request({
    url: '/commission/order/' + id,
    method: 'get'
  })
}

// 新增佣金提现
export function addOrder(data) {
  return request({
    url: '/commission/order',
    method: 'post',
    data: data
  })
}

// 修改佣金提现
export function updateOrder(data) {
  return request({
    url: '/commission/order',
    method: 'put',
    data: data
  })
}

// 删除佣金提现
export function delOrder(id) {
  return request({
    url: '/commission/order/' + id,
    method: 'delete'
  })
}
