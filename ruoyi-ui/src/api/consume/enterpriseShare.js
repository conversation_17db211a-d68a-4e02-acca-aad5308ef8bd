import request from '@/utils/request'

// 查询兑换企业股列表
export function listEnterpriseShare(query) {
  return request({
    url: '/consume/enterpriseShare/list',
    method: 'get',
    params: query
  })
}

// 查询兑换企业股详细
export function getEnterpriseShare(id) {
  return request({
    url: '/consume/enterpriseShare/' + id,
    method: 'get'
  })
}

// 新增兑换企业股
export function addEnterpriseShare(data) {
  return request({
    url: '/consume/enterpriseShare',
    method: 'post',
    data: data
  })
}

// 修改兑换企业股
export function updateEnterpriseShare(data) {
  return request({
    url: '/consume/enterpriseShare',
    method: 'put',
    data: data
  })
}

// 删除兑换企业股
export function delEnterpriseShare(id) {
  return request({
    url: '/consume/enterpriseShare/' + id,
    method: 'delete'
  })
}
