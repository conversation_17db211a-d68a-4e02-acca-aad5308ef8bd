import request from '@/utils/request'

// 查询兑换权证列表
export function listSzqz(query) {
  return request({
    url: '/consume/szqz/list',
    method: 'get',
    params: query
  })
}

// 查询兑换权证详细
export function getSzqz(id) {
  return request({
    url: '/consume/szqz/' + id,
    method: 'get'
  })
}

// 新增兑换权证
export function addSzqz(data) {
  return request({
    url: '/consume/szqz',
    method: 'post',
    data: data
  })
}

// 修改兑换权证
export function updateSzqz(data) {
  return request({
    url: '/consume/szqz',
    method: 'put',
    data: data
  })
}

// 删除兑换权证
export function delSzqz(id) {
  return request({
    url: '/consume/szqz/' + id,
    method: 'delete'
  })
}
