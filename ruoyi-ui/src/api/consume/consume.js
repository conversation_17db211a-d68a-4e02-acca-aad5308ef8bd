import request from "@/utils/request";

// 查询富星卡列表
export function listConsume(query) {
  return request({
    url: "/consume/list",
    method: "get",
    params: query,
  });
}

// 查询富星卡详细
export function getConsume(consumeNumber) {
  return request({
    url: "/consume/" + consumeNumber,
    method: "get",
  });
}

// 统计富星卡信息
export function statisticsConsume() {
  return request({
    url: "/consume/statistics",
    method: "get",
  });
}


// 修改富星卡
export function updateConsume(data) {
  return request({
    url: "/consume",
    method: "put",
    data: data,
  });
}

// 删除富星卡
export function delConsume(consumeNumber) {
  return request({
    url: "/consume/" + consumeNumber,
    method: "delete",
  });
}
