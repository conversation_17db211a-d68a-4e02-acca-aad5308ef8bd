import request from '@/utils/request'

// 查询富星卡兑换列表
export function listShare(query) {
  return request({
    url: '/consume/share/list',
    method: 'get',
    params: query
  })
}

// 查询富星卡兑换详细
export function getShare(id) {
  return request({
    url: '/consume/share/' + id,
    method: 'get'
  })
}

// 新增富星卡兑换
export function addShare(data) {
  return request({
    url: '/consume/share',
    method: 'post',
    data: data
  })
}

// 修改富星卡兑换
export function updateShare(data) {
  return request({
    url: '/consume/share',
    method: 'put',
    data: data
  })
}

// 删除富星卡兑换
export function delShare(id) {
  return request({
    url: '/consume/share/' + id,
    method: 'delete'
  })
}
