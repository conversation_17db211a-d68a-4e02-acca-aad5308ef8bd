import request from "@/utils/request";

// 查询消费卡抵押明细列表
export function listBuy(query) {
  return request({
    url: "/consume/buy/list",
    method: "get",
    params: query,
  });
}

// 查询消费卡抵押明细详细
export function getBuy(id) {
  return request({
    url: "/consume/buy/" + id,
    method: "get",
  });
}

// 修改消费卡抵押明细
export function updateBuy(data) {
  return request({
    url: "/consume/buy",
    method: "put",
    data: data,
  });
}
// 新增消费卡抵押明细
export function addBuy(data) {
  return request({
    url: "/consume/buy",
    method: "post",
    data: data,
  });
}

// 取消消费卡抵押明细
export function cancelBuy(id) {
  return request({
    url: "/consume/buy/" + id,
    method: "put",
  });
}
