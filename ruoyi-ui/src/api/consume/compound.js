import request from "@/utils/request";

// 查询富星卡合成列表
export function listCompound(query) {
  return request({
    url: "/consume/compound/list",
    method: "get",
    params: query,
  });
}

// 查询富星卡合成详细
export function getCompound(id) {
  return request({
    url: "/consume/compound/" + id,
    method: "get",
  });
}

// 新增富星卡合成
export function addCompound(data) {
  return request({
    url: "/consume/compound",
    method: "post",
    data: data,
  });
}

// 修改富星卡合成
export function updateCompound(data) {
  return request({
    url: "/consume/compound",
    method: "put",
    data: data,
  });
}

// 删除富星卡合成
export function delCompound(id) {
  return request({
    url: "/consume/compound/" + id,
    method: "delete",
  });
}
