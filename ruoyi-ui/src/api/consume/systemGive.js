import request from "@/utils/request";

// 查询系统赠送富兴卡列表
export function listSystemGive(query) {
  return request({
    url: "/consume/systemGive/list",
    method: "get",
    params: query,
  });
}

// 查询系统赠送富兴卡列表
export function listSystemGiveSum(query) {
  return request({
    url: "/consume/systemGive/listSum",
    method: "get",
    params: query,
  });
}

// 查询系统赠送富兴卡详细
export function getSystemGive(id) {
  return request({
    url: "/consume/systemGive/" + id,
    method: "get",
  });
}

// 新增系统赠送富兴卡
export function addSystemGive(data) {
  return request({
    url: "/consume/systemGive",
    method: "post",
    data: data,
  });
}

// 修改系统赠送富兴卡
export function updateSystemGive(data) {
  return request({
    url: "/consume/systemGive",
    method: "put",
    data: data,
  });
}

// 删除系统赠送富兴卡
export function delSystemGive(id) {
  return request({
    url: "/consume/systemGive/" + id,
    method: "delete",
  });
}
