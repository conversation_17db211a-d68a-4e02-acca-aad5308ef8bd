import request from '@/utils/request'

// 查询富星卡合并列表
export function listMerge(query) {
  return request({
    url: '/consume/merge/list',
    method: 'get',
    params: query
  })
}

// 查询富星卡合并详细
export function getMerge(id) {
  return request({
    url: '/consume/merge/' + id,
    method: 'get'
  })
}

// 新增富星卡合并
export function addMerge(data) {
  return request({
    url: '/consume/merge',
    method: 'post',
    data: data
  })
}

// 修改富星卡合并
export function updateMerge(data) {
  return request({
    url: '/consume/merge',
    method: 'put',
    data: data
  })
}

// 删除富星卡合并
export function delMerge(id) {
  return request({
    url: '/consume/merge/' + id,
    method: 'delete'
  })
}
