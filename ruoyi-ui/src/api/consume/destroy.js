import request from '@/utils/request'

// 查询富星卡销毁列表
export function listDestroy(query) {
  return request({
    url: '/consume/destroy/list',
    method: 'get',
    params: query
  })
}

// 查询富星卡销毁详细
export function getDestroy(id) {
  return request({
    url: '/consume/destroy/' + id,
    method: 'get'
  })
}

// 新增富星卡销毁
export function addDestroy(data) {
  return request({
    url: '/consume/destroy',
    method: 'post',
    data: data
  })
}

// 修改富星卡销毁
export function updateDestroy(data) {
  return request({
    url: '/consume/destroy',
    method: 'put',
    data: data
  })
}

// 删除富星卡销毁
export function delDestroy(id) {
  return request({
    url: '/consume/destroy/' + id,
    method: 'delete'
  })
}
