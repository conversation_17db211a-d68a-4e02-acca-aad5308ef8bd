import request from "@/utils/request";

// 查询富星卡赠送列表
export function listGive(query) {
  return request({
    url: "/consume/give/list",
    method: "get",
    params: query,
  });
}

// 查询富星卡赠送详细
export function getGive(id) {
  return request({
    url: "/consume/give/" + id,
    method: "get",
  });
}

// 新增富星卡赠送
export function addGive(data) {
  return request({
    url: "/consume/give",
    method: "post",
    data: data,
  });
}

// 修改富星卡赠送
export function updateGive(data) {
  return request({
    url: "/consume/give",
    method: "put",
    data: data,
  });
}

// 删除富星卡赠送
export function delGive(id) {
  return request({
    url: "/consume/give/" + id,
    method: "delete",
  });
}
