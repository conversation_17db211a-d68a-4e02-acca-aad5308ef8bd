import request from "@/utils/request";

// 查询富星卡拆分列表
export function listSplit(query) {
  return request({
    url: "/consume/split/list",
    method: "get",
    params: query,
  });
}

// 查询富星卡拆分详细
export function getSplit(id) {
  return request({
    url: "/consume/split/" + id,
    method: "get",
  });
}

// 新增富星卡拆分
export function addSplit(data) {
  return request({
    url: "/consume/split",
    method: "post",
    data: data,
  });
}

// 修改富星卡拆分
export function updateSplit(data) {
  return request({
    url: "/consume/split",
    method: "put",
    data: data,
  });
}

// 删除富星卡拆分
export function delSplit(id) {
  return request({
    url: "/consume/split/" + id,
    method: "delete",
  });
}
