import request from "@/utils/request";

// 查询企业股分红设置信息列表
export function listEnterpriseShare(query) {
  return request({
    url: "/seting/enterpriseShare/list",
    method: "get",
    params: query,
  });
}

// 查询企业股分红设置信息详细
export function getEnterpriseShare(id) {
  return request({
    url: "/seting/enterpriseShare/" + id,
    method: "get",
  });
}

// 新增企业股分红设置信息
export function addEnterpriseShare(data) {
  return request({
    url: "/seting/enterpriseShare",
    method: "post",
    data: data,
  });
}

// 修改企业股分红设置信息
export function updateEnterpriseShare(data) {
  return request({
    url: "/seting/enterpriseShare",
    method: "put",
    data: data,
  });
}

// 删除企业股分红设置信息
export function delEnterpriseShare(id) {
  return request({
    url: "/seting/enterpriseShare/" + id,
    method: "delete",
  });
}

// 查询企业股分红明细信息列表
export function listDetail(query) {
  return request({
    url: "/seting/enterpriseShare/detail-list",
    method: "get",
    params: query,
  });
}
