import request from '@/utils/request'

// 查询APP版本信息列表
export function listVersion(query) {
  return request({
    url: '/seting/version/list',
    method: 'get',
    params: query
  })
}

// 查询APP版本信息详细
export function getVersion(id) {
  return request({
    url: '/seting/version/' + id,
    method: 'get'
  })
}

// 新增APP版本信息
export function addVersion(data) {
  return request({
    url: '/seting/version',
    method: 'post',
    data: data
  })
}

// 修改APP版本信息
export function updateVersion(data) {
  return request({
    url: '/seting/version',
    method: 'put',
    data: data
  })
}

// 删除APP版本信息
export function delVersion(id) {
  return request({
    url: '/seting/version/' + id,
    method: 'delete'
  })
}
