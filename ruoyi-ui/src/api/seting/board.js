import request from "@/utils/request";

// 查询订单支付数据
export function orderPayLine(query) {
  return request({
    url: "/capital/orderPayLine",
    method: "get",
    params: query,
  });
}

// 查询订单笔数数据
export function orderCountLine(query) {
  return request({
    url: "/capital/orderCountLine",
    method: "get",
    params: query,
  });
}
// 查询订单板块笔数数据
export function orderPlateCountLine(query) {
  return request({
    url: "/capital/orderPlateCountLine",
    method: "get",
    params: query,
  });
}
// 查询订单利润数据
export function orderProfitAmount(query) {
  return request({
    url: "/capital/orderProfitAmount",
    method: "get",
    params: query,
  });
}

// 获取板块订单利润总额
export function orderPlateCountProfitAmountLine(query) {
  return request({
    url: "/capital/orderPlateProfitAmountLine",
    method: "get",
    params: query,
  });
}
// 查询订单销售总额
export function orderSalesAmount(query) {
  return request({
    url: "/capital/orderSalesAmount",
    method: "get",
    params: query,
  });
}

// 查询订单销售总额
export function advertisement(query) {
  return request({
    url: "/capital/advertisement",
    method: "get",
    params: query,
  });
}
// 获取支付宝累计金额数据
export function aliPayAmount(query) {
  return request({
    url: "/capital/aliPayAmount",
    method: "get",
    params: query,
  });
}
