import request from "@/utils/request";

// 查询参数设置信息列表
export function listParameter(query) {
  return request({
    url: "/seting/parameter/list",
    method: "get",
    params: query,
  });
}

// 查询参数设置信息详细
export function getParameter(type) {
  return request({
    url: "/seting/parameter/" + type,
    method: "get",
  });
}

// 新增参数设置信息
export function addParameter(data) {
  return request({
    url: "/seting/parameter",
    method: "post",
    data: data,
  });
}
