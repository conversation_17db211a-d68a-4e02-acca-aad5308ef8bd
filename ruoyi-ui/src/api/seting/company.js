import request from '@/utils/request'

// 查询合资公司列表
export function listCompany(query) {
  return request({
    url: '/seting/company/list',
    method: 'get',
    params: query
  })
}

// 查询合资公司详细
export function getCompany(id) {
  return request({
    url: '/seting/company/' + id,
    method: 'get'
  })
}

// 新增合资公司
export function addCompany(data) {
  return request({
    url: '/seting/company',
    method: 'post',
    data: data
  })
}

// 修改合资公司
export function updateCompany(data) {
  return request({
    url: '/seting/company',
    method: 'put',
    data: data
  })
}

// 删除合资公司
export function delCompany(id) {
  return request({
    url: '/seting/company/' + id,
    method: 'delete'
  })
}
