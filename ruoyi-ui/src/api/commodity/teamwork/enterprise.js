import request from "@/utils/request";

// 查询品牌购拼团信息列表
export function listEnterprise(query) {
  return request({
    url: "/teamwork/enterprise/list",
    method: "get",
    params: query,
  });
}

// 查询品牌购拼团信息详细
export function getEnterprise(id) {
  return request({
    url: "/teamwork/enterprise/" + id,
    method: "get",
  });
}

// 新增品牌购拼团信息
export function addEnterprise(data) {
  return request({
    url: "/teamwork/enterprise",
    method: "post",
    data: data,
  });
}

// 修改品牌购拼团信息
export function updateEnterprise(data) {
  return request({
    url: "/teamwork/enterprise",
    method: "put",
    data: data,
  });
}

// 取消品牌购拼团信息
export function teamworkCancel(id) {
  return request({
    url: "/teamwork/enterprise/" + id,
    method: "put",
  });
}
