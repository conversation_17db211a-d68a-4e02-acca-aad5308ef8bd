import request from "@/utils/request";

// 查询拼团购拼团信息列表
export function listGroup(query) {
  return request({
    url: "/teamwork/group/list",
    method: "get",
    params: query,
  });
}

// 查询拼团购拼团信息详细
export function getGroup(id) {
  return request({
    url: "/teamwork/group/" + id,
    method: "get",
  });
}

// 新增拼团购拼团信息
export function addGroup(data) {
  return request({
    url: "/teamwork/group",
    method: "post",
    data: data,
  });
}

// 修改拼团购拼团信息
export function updateGroup(data) {
  return request({
    url: "/teamwork/group",
    method: "put",
    data: data,
  });
}

// 取消品牌购拼团信息
export function teamworkCancel(id) {
  return request({
    url: "/teamwork/group/" + id,
    method: "put",
  });
}
