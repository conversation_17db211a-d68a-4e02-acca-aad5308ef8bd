import request from "@/utils/request";

// 查询商品管理列表
export function listInfo(query) {
  return request({
    url: "/commodity/info/list",
    method: "get",
    params: query,
  });
}

// 查询商品管理详细
export function getInfo(id) {
  return request({
    url: "/commodity/info/" + id,
    method: "get",
  });
}

// 新增商品管理
export function addInfo(data) {
  return request({
    url: "/commodity/info",
    method: "post",
    data: data,
  });
}

// 修改商品管理
export function updateInfo(data) {
  return request({
    url: "/commodity/info",
    method: "put",
    data: data,
  });
}

// 删除商品管理
export function delInfo(id) {
  return request({
    url: "/commodity/info/" + id,
    method: "delete",
  });
}
// 商品信息上架
export function shelf(id) {
  return request({
    url: "/commodity/info/shelf/" + id,
    method: "get",
  });
}

// 商品信息下架
export function offShel(id) {
  return request({
    url: "/commodity/info/offShel/" + id,
    method: "get",
  });
}

// 商品信息排序
export function sortNumber(data) {
  return request({
    url: "/commodity/info/sortNumber",
    method: "put",
    data: data,
  });
}

// 商品信息排序
export function topUpTime(id) {
  return request({
    url: "/commodity/info/topUpTime/" + id,
    method: "get",
  });
}

// 商品信息排序
export function cancelTopUpTime(id) {
  return request({
    url: "/commodity/info/cancelTopUpTime/" + id,
    method: "get",
  });
}

// 商品信息同步
export function dingdongSync(id) {
  return request({
    url: "/commodity/info/dingdongSync/" + id,
    method: "get",
  });
}
