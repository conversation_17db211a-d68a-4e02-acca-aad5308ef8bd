import request from '@/utils/request'

// 查询订单物流信息列表
export function listDelivery(query) {
  return request({
    url: '/commodity/delivery/list',
    method: 'get',
    params: query
  })
}

// 查询订单物流信息详细
export function getDelivery(id) {
  return request({
    url: '/commodity/delivery/' + id,
    method: 'get'
  })
}

// 新增订单物流信息
export function addDelivery(data) {
  return request({
    url: '/commodity/delivery',
    method: 'post',
    data: data
  })
}

// 修改订单物流信息
export function updateDelivery(data) {
  return request({
    url: '/commodity/delivery',
    method: 'put',
    data: data
  })
}

// 删除订单物流信息
export function delDelivery(id) {
  return request({
    url: '/commodity/delivery/' + id,
    method: 'delete'
  })
}
