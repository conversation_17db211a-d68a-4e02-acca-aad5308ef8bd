import request from '@/utils/request'

// 查询富星拼团购列表
export function listGroupBuying(query) {
  return request({
    url: '/commodity/groupBuying/list',
    method: 'get',
    params: query
  })
}

// 查询富星拼团购详细
export function getGroupBuying(id) {
  return request({
    url: '/commodity/groupBuying/' + id,
    method: 'get'
  })
}

// 新增富星拼团购
export function addGroupBuying(data) {
  return request({
    url: '/commodity/groupBuying',
    method: 'post',
    data: data
  })
}

// 修改富星拼团购
export function updateGroupBuying(data) {
  return request({
    url: '/commodity/groupBuying',
    method: 'put',
    data: data
  })
}

// 删除富星拼团购
export function delGroupBuying(id) {
  return request({
    url: '/commodity/groupBuying/' + id,
    method: 'delete'
  })
}
