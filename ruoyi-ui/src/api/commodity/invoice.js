import request from '@/utils/request'

// 查询商品订单发票信息列表
export function listInvoice(query) {
  return request({
    url: '/commodity/invoice/list',
    method: 'get',
    params: query
  })
}

// 查询商品订单发票信息详细
export function getInvoice(id) {
  return request({
    url: '/commodity/invoice/' + id,
    method: 'get'
  })
}

// 新增商品订单发票信息
export function addInvoice(data) {
  return request({
    url: '/commodity/invoice',
    method: 'post',
    data: data
  })
}

// 修改商品订单发票信息
export function updateInvoice(data) {
  return request({
    url: '/commodity/invoice',
    method: 'put',
    data: data
  })
}

// 删除商品订单发票信息
export function delInvoice(id) {
  return request({
    url: '/commodity/invoice/' + id,
    method: 'delete'
  })
}
