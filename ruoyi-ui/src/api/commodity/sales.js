import request from '@/utils/request'

// 查询订单售后信息列表
export function listSales(query) {
  return request({
    url: '/commodity/sales/list',
    method: 'get',
    params: query
  })
}

// 查询订单售后信息详细
export function getSales(commodityOrderId) {
  return request({
    url: '/commodity/sales/' + commodityOrderId,
    method: 'get'
  })
}

// 新增订单售后信息
export function addSales(data) {
  return request({
    url: '/commodity/sales',
    method: 'post',
    data: data
  })
}

// 修改订单售后信息
export function updateSales(data) {
  return request({
    url: '/commodity/sales',
    method: 'put',
    data: data
  })
}

// 删除订单售后信息
export function delSales(commodityOrderId) {
  return request({
    url: '/commodity/sales/' + commodityOrderId,
    method: 'delete'
  })
}
