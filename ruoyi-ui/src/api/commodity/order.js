import request from "@/utils/request";

// 查询商品订单信息列表
export function listOrder(query) {
  return request({
    url: "/commodity/order/list",
    method: "get",
    params: query,
  });
}

// 查询商品订单信息详细
export function getOrder(id) {
  return request({
    url: "/commodity/order/" + id,
    method: "get",
  });
}

// 修改商品订单信息
export function updateOrder(data, url, key) {
  return request({
    url: "/commodity/order/" + url + "/" + key,
    method: "put",
    data: data,
  });
}
// 供应链商品订单信息重新支付
export function dingdongRestartPayment(key) {
  return request({
    url: "/commodity/order/dingdongRestartPayment/" + key,
    method: "post",
  });
}
